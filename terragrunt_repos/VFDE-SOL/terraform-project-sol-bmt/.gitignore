.DS_STORE
.tool-versions
.envrc

# Local .terraform directories
**/.terraform/*

# .tfstate files
*.tfstate
*.tfstate.*

# Crash log files
crash.log

# Ignore any .tfvars files that are generated automatically for each Terraform run. Most
# .tfvars files are managed as part of configuration and so should be included in
# version control.
#
# example.tfvars

# Ignore override files as they are usually used to override resources locally and so
# are not checked in
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Include override files you do wish to add to version control using negated pattern
#
# !example_override.tf

# Include tfplan files to ignore the plan output of command: terraform plan -out=tfplan
# example: *tfplan*

# terragrunt ignores
.terragrunt-cache
.terraform
terragrunt-debug.tfvars.json

# ignore terraform lock files under live and modules created by linters
live/**/.terraform.lock.hcl
modules/**/.terraform.lock.hcl

# ignore .bulldozer.yml because of incopatibility with atlantis (automerge feature)
.bulldozer.yml
