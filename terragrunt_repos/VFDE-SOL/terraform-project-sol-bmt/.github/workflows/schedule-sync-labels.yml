name: schedule-sync-labels

on:
  schedule:
    - cron:  "0 4 * * 6" # “At 04:00 GMT-0 on Saturday”
  workflow_dispatch:

jobs:
  sync:
    runs-on: self-hosted
    steps:
      - name: (pre) Install ghcli
        uses: VFDE-SOL/actions/install-ghcli@v1

      - name: (pre) Install yq4
        uses: VFDE-SOL/actions/install-yq4@v1

      - name: (pre) Generate Token
        id: generate_token
        uses: VFDE-SOL/mirror-action-tibdex-github-app-token@v2
        with:
          app_id: ${{ secrets.APP_SOL_BOT_ID }}
          private_key: ${{ secrets.APP_SOL_BOT_PRIVATE_KEY }}

      - name: (pre) Setup ghcli auth
        uses: VFDE-SOL/actions/ghcli-authenticate@v1
        with:
          gh_token: ${{ steps.generate_token.outputs.token }}

      - name: Checkout
        uses: actions/checkout@v4

      - name: Save existing octopilot labels
        shell: bash
        run: |
          labels_json=$(gh label list --search "octopilot" --limit 100 --json "name,color,description")
          if [ -n "${labels_json}" ]; then
            echo "::debug::Set octopilot labels"
            echo "${labels_json}" | yq4 -P > .github/config/labels_octopilot.yml
            echo "SYNC_LABELS_ADDITIONAL_CONFIG_FILE=.github/config/labels_octopilot.yml" >> $GITHUB_ENV
          else
            echo "::debug::No octopilot labels set"
            echo "SYNC_LABELS_ADDITIONAL_CONFIG_FILE=" >> $GITHUB_ENV
          fi

      - name: Label Sync
        uses: VFDE-SOL/actions/sync-labels@v1
        with:
          additional_config_file: |
            .github/config/labels_tf.yml
            ${{ env.SYNC_LABELS_ADDITIONAL_CONFIG_FILE }}
