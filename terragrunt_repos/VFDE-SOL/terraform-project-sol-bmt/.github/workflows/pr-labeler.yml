name: pr-labeler

on: pull_request

jobs:
  size-label:
    runs-on: self-hosted
    if: >-
      github.event.pull_request.user.login != 'sol-bot[bot]' &&
      github.event.pull_request.user.login != 'sol-github-org-repos[bot]'
    steps:
      - name: Generate Token
        id: generate_token
        uses: VFDE-SOL/mirror-action-actions-create-github-app-token@v1
        with:
          app-id: ${{ secrets.APP_SOL_LABELER_ID }}
          private-key: ${{ secrets.APP_SOL_LABELER_PRIVATE_KEY }}

      - name: size-label
        uses: VFDE-SOL/mirror-action-pascalgn-size-label-action@vfde
        env:
          GITHUB_TOKEN: ${{ steps.generate_token.outputs.token }}
        with:
          sizes: >
            {
              "0": "XS",
              "10": "S",
              "30": "M",
              "100": "L",
              "500": "XL",
              "1000": "XXL"
            }
