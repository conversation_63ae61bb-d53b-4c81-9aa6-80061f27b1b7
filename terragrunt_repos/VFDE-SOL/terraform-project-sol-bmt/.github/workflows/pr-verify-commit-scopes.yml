name: pr-verify-commit-scopes

on:
  pull_request:
    types: [ opened, synchronize ]

jobs:
  main:
    runs-on: self-hosted
    name: pr-verify-commit-scopes
    if: >-
      github.event.pull_request.user.login != 'sol-bot[bot]' &&
      github.event.pull_request.user.login != 'sol-github-org-repos[bot]'
    steps:
      - name: Generate Token
        id: generate_token
        uses: VFDE-SOL/mirror-action-actions-create-github-app-token@v1
        with:
          app-id: ${{ secrets.APP_SOL_BOT_ID }}
          private-key: ${{ secrets.APP_SOL_BOT_PRIVATE_KEY }}

      - uses: VFDE-SOL/actions/verify-commit-scopes@v1
        with:
          regex: "env/([^\\/]+)/"
          github_token: ${{ steps.generate_token.outputs.token }}
