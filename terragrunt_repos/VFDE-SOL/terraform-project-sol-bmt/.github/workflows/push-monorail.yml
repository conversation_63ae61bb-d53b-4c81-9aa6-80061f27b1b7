name: push-monorail

on:
  push:
    branches: [ "master" ]

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: false

jobs:
  helper:
    name: main / helper
    runs-on: self-hosted
    if: github.repository == 'VFDE-SOL/template-terraform-project'
    outputs:
      previoustag: ${{ steps.previoustag.outputs.tag }}

    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0 # Required due to the way Git works, without it this action won't be able to find any or the correct tags

      - name: Get Previous tag
        id: previoustag
        uses: VFDE-SOL/mirror-action-WyriHaximus-github-action-get-previous-tag@v1
        with:
          fallback: NA

  monorail:
    uses: VFDE-ISS/workflows/.github/workflows/monorail.yaml@monorail/v1
    if: github.repository == 'VFDE-SOL/template-terraform-project'
    secrets: # Optional, but needed if workflows should react on tags.
      app_id: ${{ secrets.APP_SOL_MONORAIL_BOT_ID }}
      app_private_key: ${{ secrets.APP_SOL_MONORAIL_BOT_PRIVATE_KEY }}
    with:
      dashboard_enabled: false

  create-release:
    name: main / create-release
    runs-on: self-hosted
    needs: [ "monorail", "helper" ]
    steps:
      - name: Generate Token
        id: generate_token
        uses: VFDE-SOL/mirror-action-tibdex-github-app-token@v1
        with:
          app_id: ${{ secrets.APP_SOL_MONORAIL_BOT_ID }}
          private_key: ${{ secrets.APP_SOL_MONORAIL_BOT_PRIVATE_KEY }}

      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0 # Required due to the way Git works, without it this action won't be able to find any or the correct tags

      - name: Get latest tag
        id: previoustag
        uses: VFDE-SOL/mirror-action-WyriHaximus-github-action-get-previous-tag@v1
        with:
          fallback: NA

      - name: Update CHANGELOG
        id: changelog
        if: >-
          needs.helper.outputs.previoustag != 'NA'
        uses: VFDE-SOL/mirror-action-requarks-changelog-action@v1
        with:
          token: ${{ github.token }}
          fromTag: ${{ steps.previoustag.outputs.tag }}
          toTag: ${{ needs.helper.outputs.previoustag }}
          writeToFile: false

      - name: Create Release
        uses: VFDE-SOL/mirror-action-ncipollo-release-action@v1
        if: >-
          steps.previoustag.outputs.tag != 'NA'
        with:
          allowUpdates: false
          draft: false
          tag: ${{ steps.previoustag.outputs.tag }}
          body: ${{ steps.changelog.outputs.changes }}
          token: ${{ steps.generate_token.outputs.token }}
