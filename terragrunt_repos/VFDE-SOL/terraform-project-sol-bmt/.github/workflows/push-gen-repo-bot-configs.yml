name: push-gen-repo-bot-configs

on:
  push:
    branches: ["master"]
    paths:
      - .policy.yml.tpl
      - README.md
  workflow_dispatch:

jobs:
  push-gen-repo-bot-configs:
    runs-on: self-hosted
    steps:
      - name: Generate Token
        id: generate_token
        uses: VFDE-SOL/mirror-action-actions-create-github-app-token@v1
        with:
          app-id: ${{ secrets.APP_SOL_BOT_ID }}
          private-key: ${{ secrets.APP_SOL_BOT_PRIVATE_KEY }}

      - name: Update .policy.yml and .settingsbot.yml file
        uses: VFDE-SOL/actions/gen-repo-bot-configs@gen-repo-bot-configs/v0
        with:
          gh_token: ${{ steps.generate_token.outputs.token }}
