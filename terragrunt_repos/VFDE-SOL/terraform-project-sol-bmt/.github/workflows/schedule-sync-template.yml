name: schedule-sync-template
on:
  schedule:
    - cron:  "45 5 * * 1-5" # “At 05:45 GMT-0 on every day-of-week”
  workflow_dispatch:

jobs:
  sync-tpl-files:
    runs-on: self-hosted
    if: github.repository != 'VFDE-SOL/template-terraform-project'
    steps:
      - name: Generate GitHub App Token
        id: app_token
        uses: VFDE-SOL/mirror-action-tibdex-github-app-token@v2
        with:
          app_id: ${{ secrets.APP_ORG_REPOS_RW_ID }}
          private_key: ${{ secrets.APP_ORG_REPOS_RW_PRIVATE_KEY }}

      - name: Install ghcli
        uses: VFDE-SOL/actions/install-ghcli@v1

      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
          token: ${{ steps.app_token.outputs.token }}

      - name: actions-template-sync
        uses: VFDE-SOL/mirror-action-AndreasAugustin-actions-template-sync@v2
        with:
          source_gh_token: ${{ steps.app_token.outputs.token }}
          target_gh_token: ${{ steps.app_token.outputs.token }}
          hostname: github.vodafone.com
          source_repo_path: VFDE-SOL/template-terraform-project
          upstream_branch: master
          pr_title: "chore(sync-tpl-files): upstream merge template repository"
          pr_labels: "chore,kind/template-sync"
          pr_commit_msg: "chore(template): merge template changes"
          git_remote_pull_params: v0 --allow-unrelated-histories --squash --strategy=recursive -X theirs
