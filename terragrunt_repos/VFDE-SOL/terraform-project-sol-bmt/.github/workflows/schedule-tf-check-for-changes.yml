name: schedule-tf-check-for-changes

on:
  schedule:
    - cron:  "15 2 * * 1-5" # “At 02:15 GMT-0 on every day-of-week”
  workflow_dispatch:

permissions:
  id-token: write # This is required for requesting the JWT (AWS Credentials)
  contents: read  # This is required for actions/checkout
  issues: write

jobs:
  get-project-environments:
    runs-on: self-hosted-long-running
    if: github.repository != 'VFDE-SOL/template-terraform-project'

    outputs:
      project-envs: ${{ steps.set-matrix.outputs.matrix }}

    steps:
    # Checkout-------------------------------------------------------------------------------
    - name: Checkout repo
      uses: actions/checkout@v4

    # Intall prerequisite packages/tools ----------------------------------------------------
    - name: (pre) Install jq
      uses: VFDE-SOL/actions/install-jq@v1

    # Final job ------------------------------------------------------------------------------
    - name: Get project environments
      id: set-matrix
      shell: bash
      run: |
        envs=()
        for d in ./env/*/ ; do
            envs+=("$(basename "$d")")
        done

        envs_as_json=$(jq --compact-output --null-input '$ARGS.positional' --args -- "${envs[@]}")
        echo "matrix={\"project-env\":${envs_as_json}}" >> $GITHUB_OUTPUT
        # DEVELOP :: echo "matrix={\"project-env\":[\"sbox2-vfcf\"]}" >> $GITHUB_OUTPUT

  main:
    name: check-for-tf-changes
    runs-on: self-hosted-long-running
    if: github.repository != 'VFDE-SOL/template-terraform-project'
    needs: get-project-environments

    strategy:
      fail-fast: false
      matrix: ${{ fromJSON(needs.get-project-environments.outputs.project-envs) }}

    env:
      CACHE_VERSION: '001'
      TF_PLUGIN_CACHE_DIR: /home/<USER>/.local/.terraform.d/plugin-cache
      TG_PARALLELISM: 1

    steps:
    # Checkout-------------------------------------------------------------------------------
    - name: Checkout repo
      uses: actions/checkout@v4

    # Intall prerequisite packages/tools ----------------------------------------------------

    - name: (pre) Install hcledit
      uses: VFDE-SOL/actions/install-hcledit@v1

    - name: (pre) Install jq
      uses: VFDE-SOL/actions/install-jq@v1

    - name: (pre) Install ghcli
      uses: VFDE-SOL/actions/install-ghcli@v1

    - name: (pre) Install go
      uses: actions/setup-go@v3
      with:
        go-version: '>=1.21'

    - name: (pre) Install sol-cet-devtools
      uses: VFDE-SOL/actions/install-vfde-sol-cet-devtools@v1

    - name: (pre) Install cetops
      uses: VFDE-SOL/actions/install-vfde-sol-cetops@v1


    # Configuration/Authentification --------------------------------------------------------
    - name: (conf) Configure AWS Credentials
      uses: VFDE-SOL/mirror-action-aws-actions-configure-aws-credentials@v4
      env:
        AWS_REGION : "eu-central-1"
      with:
        role-to-assume: ${{ secrets.TERRAGRUNT_EXECUTIONER_ROLE_ARN }}
        aws-region: ${{ env.AWS_REGION }}
        role-session-name: ${{ env.GITHUB_REPOSITORY }}
        role-duration-seconds: 7200

    # generate token to enable terragrunt to access private repos
    - name: (conf) Generate Token using SOL-BOT
      id: generate_token
      uses: VFDE-SOL/mirror-action-actions-create-github-app-token@v2
      with:
        app-id: ${{ secrets.APP_SOL_BOT_ID }}
        private-key: ${{ secrets.APP_SOL_BOT_PRIVATE_KEY }}
        owner: ${{ github.repository_owner }}

    - name: (conf) ghcli-authenticate
      uses: VFDE-SOL/actions/ghcli-authenticate@v1
      with:
        gh_token: ${{ steps.generate_token.outputs.token }}

    - name: (conf) git-credential-helper-gh
      uses: VFDE-SOL/actions/git-credential-helper-gh@v1

    - name: (conf) git-credential-helper-codecommit
      uses: VFDE-SOL/actions/git-credential-helper-codecommit@v1


    # Final job ------------------------------------------------------------------------------

    - name: (setup) Setup tg-exec cache
      uses: VFDE-SOL/mirror-action-actions-cache/restore@v3
      id: cache-tg-exec
      with:
        path: |
          ${{ env.TF_PLUGIN_CACHE_DIR }}
        key: tg-exec-${{ github.repository_id }}-${{ matrix.project-env }}-${{ env.CACHE_VERSION }}-${{ github.sha }}
        restore-keys: |
          tg-exec-${{ github.repository_id }}-${{ matrix.project-env }}-${{ env.CACHE_VERSION }}-

    - name: (setup) Setup terragrunt download cache dir
      if: steps.cache-tg-exec.outputs.cache-hit != 'true'
      shell: bash
      run : |
        echo "::notice:: Creating terragrunt download cache dir"
        mkdir -p "${TF_PLUGIN_CACHE_DIR}"

      # ref: https://terragrunt.gruntwork.io/docs/reference/cli-options/#terragrunt-provider-cache
    - name: Enable Terragrunt’s provider caching
      run: |
        echo "TERRAGRUNT_PROVIDER_CACHE=1" | tee -a "$GITHUB_ENV"

      # ref: https://terragrunt.gruntwork.io/docs/reference/cli-options/#terragrunt-forward-tf-stdout
    - name: Print output of Terraform as is
      run: |
        echo "TERRAGRUNT_FORWARD_TF_STDOUT=1" | tee -a "$GITHUB_ENV"

    - name: (final) Get aws account for env
      id: get-account-number
      shell: bash
      run : |
        account_number=$(hcledit attribute get locals.account_id < ./env/${{ matrix.project-env }}/terragrunt.hcl | jq -r)
        echo "::debug::account_number: ${account_number}"
        echo "account_number=${account_number}" >> $GITHUB_OUTPUT

    - name: (final) Exec terragrunt plan
      id: tg-plan
      shell: bash
      run: |
        echo '::group:: exec terragrunt plan ...'
        echo "project-env: ${{ matrix.project-env }}"
        echo "account_number: ${{ steps.get-account-number.outputs.account_number }}"
        echo '::endgroup::'

        cicd_role="arn:aws:iam::${{ steps.get-account-number.outputs.account_number }}:role/CicdDeployment"

        cd ./env/${{ matrix.project-env }}

        # get list of modules according to their dependency hierarchy
        executionGroupsJson=$(cetops infra deps -fj)
        jsonlength=$(($(jq 'length' <<< "$executionGroupsJson") -1))
        declare -a executionGroupsArray
        for (( i=0; i<=$jsonlength; i++ ))
        do
          readarray -t tmp < <(jq -r '.["'$i'"] | .[]' <<< "$executionGroupsJson")
          executionGroupsArray+=("${tmp[@]}")
        done

        # plan all modules, if plan fails add them to dirtymodules list
        # begin building an output file, later we can check its length for comparation with max gh issue length
        echo 'dirtymodules<<EOF' >> dirtymodulesoutputfile
        for module in "${executionGroupsArray[@]}"
        do
          # deletes local cashed to avoid provider/init issues
          find . -name ".terragrunt-cache" -type d -exec rm -rf {} + > /dev/null
          pushd $module > /dev/null
          printf "==================== NOW PLANING $module ====================\n"
          #we need that to get exit code from tf plan without failing the action
          set +e
          tgoutput=$(terragrunt plan \
            --terragrunt-iam-role "${cicd_role}" \
            --terragrunt-non-interactive \
            -compact-warnings \
            -detailed-exitcode \
            -no-color \
            -lock=false \
            -out=tfplan 2>&1)
          exitcode=$?
          if [ $exitcode -eq 2 ]; then
            tgoutput=$(terragrunt show tfplan \
              --terragrunt-iam-role "${cicd_role}" \
              --terragrunt-non-interactive \
              -compact-warnings \
              -no-color)
          fi
          set -e
          popd > /dev/null
          if [ $exitcode -gt 0 ]; then
            dirtymodules+=("${module}")
            printf -- "<details>\n" >> dirtymodulesoutputfile
            printf -- "<summary>%s</summary>\n" "${module#*/env/${{ matrix.project-env }}/}" >> dirtymodulesoutputfile
            printf -- "\n" >> dirtymodulesoutputfile
            printf -- "\`\`\`\n" >> dirtymodulesoutputfile
            printf "%s\n" "${tgoutput}" | tee -a dirtymodulesoutputfile
            printf -- "\`\`\`\n" >> dirtymodulesoutputfile
            printf -- "</details>\n" >> dirtymodulesoutputfile
          else
            printf -- "%s\n" "No changes."
          fi
        done
        echo EOF >> dirtymodulesoutputfile

        if [ ${#dirtymodules[@]} -gt 0 ]; then
          printf -- "\n%s\n" "=================== FILE SUCCESSFULLY BUILD ==================="
          printf -- "\n%s\n" "==================== LIST OF DIRTY MODULES ===================="
          printf -- "- %s\n" "${dirtymodules[@]#*/env/${{ matrix.project-env }}/}"

          printf -- "\n%s\n" "============== CONTENT OF dirtymodulesoutputfile ================="
          dirtymodulesoutput=$(<dirtymodulesoutputfile)
          printf "%s\n" "${dirtymodulesoutput}"

          # check if detailed output of planings is too long for gh issue
          # if too long, create a simple list of dirty modules without planing output
          if [ ${#dirtymodulesoutput} -gt 64000 ]; then
            dirtymodulesoutput=$(
              echo 'dirtymodules<<EOF'
              printf 'No detailed planing output possible because there are too many dirty modules.\n'
              for key in "${!dirtymodules[@]}"
              do
                printf -- "- %s\n" "${dirtymodules[$key]#*/env/dev-vfcf/}"
              done
              echo EOF
            )
          fi

          printf "%s\n" "$dirtymodulesoutput" >> "$GITHUB_OUTPUT"
          echo "SUCCESSFULL EXECUTION"
          echo "is_dirty=true" >> $GITHUB_OUTPUT
        else
          printf "\n%s\n" "==================== NO DIRTY MODULES ====================\n"
          echo "is_dirty=false" >> $GITHUB_OUTPUT
        fi

    # post processing / error handling ----------------------------------------------------------------------------
    - name: (post-always) Save cache-tg-exec cache
      if: ${{ always() }}
      uses: VFDE-SOL/mirror-action-actions-cache/save@v3
      with:
        path: |
          ${{ env.TF_PLUGIN_CACHE_DIR }}
        key: ${{ steps.cache-tg-exec.outputs.cache-primary-key }}

    - name: (post-always) Get issue
      if: ${{ always() }}
      uses: VFDE-SOL/mirror-action-actions-cool-issues-helper@feat/vfde
      id: find_issue
      with:
        actions: find-issues
        issue-state: 'open'
        title-includes: "[DIRTY STATE] ${{ matrix.project-env }} - account is in a dirty state"
        issue-number: ''

    - name: (post-success) Add comment before closing issue if exists
      if: ${{ steps.tg-plan.outputs.is_dirty == 'false' && fromJSON(steps.find_issue.outputs.issues)[0] != null }}
      uses: VFDE-SOL/mirror-action-actions-cool-issues-helper@feat/vfde
      with:
        actions: create-comment
        issue-number: ${{ fromJSON(steps.find_issue.outputs.issues)[0].number }}
        body: |
          Dirty state seems to fixed

          Please check job for more details:

          **project-env:** ${{ matrix.project-env }}
          **github Workflow:** ${{ github.workflow }}
          **github workflow url:** ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}

          Closing issue now, beep beep :octocat:
        emoji: '+1,heart'

    - name: (post-success) Close issue if exists
      if: ${{ steps.tg-plan.outputs.is_dirty == 'false' && fromJSON(steps.find_issue.outputs.issues)[0] != null }}
      uses: VFDE-SOL/mirror-action-actions-cool-issues-helper@feat/vfde
      with:
        actions: close-issue
        issue-number: ${{ fromJSON(steps.find_issue.outputs.issues)[0].number }}
        close-reason: completed

    - name: (post-failure) Create new issue
      if: ${{ steps.tg-plan.outputs.is_dirty == 'true' && (fromJSON(steps.find_issue.outputs.issues)[0] == null) && !startsWith(matrix.project-env, 'dev' ) && !startsWith(matrix.project-env, 'sbox' ) }}
      uses: VFDE-SOL/mirror-action-actions-cool-issues-helper@feat/vfde
      with:
        actions: create-issue
        title: "[DIRTY STATE] ${{ matrix.project-env }} - account is in a dirty state"
        body: |
          ## Dirty state(s) detected in github action run

          **Environment:**
          ${{ matrix.project-env }}

          **Dirty Modules:**
          ${{ steps.tg-plan.outputs.dirtymodules }}

          **Please re-apply those modules with Terragrunt and re-run the tf-check-for-changes action to resolve this issue.**



          Please check job for more details:
          **github Workflow:** ${{ github.workflow }}
          **github workflow url:** ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}

        labels: tf/dirty-state

    - name: (post-failure) Update existing issue
      if: ${{ steps.tg-plan.outputs.is_dirty == 'true' && (fromJSON(steps.find_issue.outputs.issues)[0] != null) && !startsWith(matrix.project-env, 'dev' ) && !startsWith(matrix.project-env, 'sbox' ) }}
      uses: VFDE-SOL/mirror-action-actions-cool-issues-helper@feat/vfde
      with:
        actions: update-issue
        title: "[DIRTY STATE] ${{ matrix.project-env }} - account is in a dirty state"
        issue-number: ${{ fromJSON(steps.find_issue.outputs.issues)[0].number }}
        body: |
          ## Dirty state(s) detected in github action run

          **Environment:**
          ${{ matrix.project-env }}

          **Dirty Modules:**
          ${{ steps.tg-plan.outputs.dirtymodules }}

          **Please re-apply those modules with Terragrunt and re-run the tf-check-for-changes action to resolve this issue.**



          Please check job for more details:
          **github Workflow:** ${{ github.workflow }}
          **github workflow url:** ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
        labels: tf/dirty-state
