name: pr-notify-tf-destruction

on:
  pull_request:
    branches: [ "master" ]
    types: [ opened, synchronize, reopened ]

jobs:
  main:
    runs-on: self-hosted
    name: pr-notify-tf-destruction
    if: >-
      github.event.pull_request.user.login != 'sol-bot[bot]' &&
      github.event.pull_request.user.login != 'sol-github-org-repos[bot]'
    steps:
      - name: Generate Token
        id: generate-token
        uses: VFDE-SOL/mirror-action-actions-create-github-app-token@v1
        with:
          app-id: ${{ secrets.APP_SOL_BOT_ID }}
          private-key: ${{ secrets.APP_SOL_BOT_PRIVATE_KEY }}

      - name: Checkout repository base
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.base.sha }}

      - name: Checkout repository head
        uses: actions/checkout@v4
        with:
          clean: false
          ref: ${{ github.event.pull_request.head.sha }}

      - name: List deleted modules
        id: deleted-modules
        shell: bash
        run: |
          changed_modules=$(git diff --name-only --diff-filter=D ${{ github.event.pull_request.base.sha }} ${{ github.event.pull_request.head.sha }} \
            | grep '\.hcl$' \
            | xargs -n1 dirname 2>/dev/null || echo "" )

          if [[ "${#changed_modules}" -gt 0 ]]; then
            echo "::debug:: Changed modules: ${changed_modules}"
            delimiter="$(openssl rand -hex 8)"
            result=$( (echo "${changed_modules}" \
              | sort -u \
              | jq -R . \
              | jq -s . ) || echo "[]")

            echo "available=true" | tee -a "$GITHUB_OUTPUT"
            echo "result<<${delimiter}" >> "$GITHUB_OUTPUT"
            for mod in $(echo "${result}" | jq -r '.[]'); do
                echo "- ${mod}" | tee -a "$GITHUB_OUTPUT"
            done
            echo "${delimiter}" >> "$GITHUB_OUTPUT"
          else
            echo "::debug:: No changed modules detected"
            echo "available=false" | tee -a "$GITHUB_OUTPUT"
            echo "result=[]" | tee -a "$GITHUB_OUTPUT"
          fi

      - name: Comment on PR if modules will be deleted
        if: steps.deleted-modules.outputs.available=='true'
        uses: VFDE-SOL/mirror-action-thollander-actions-comment-pull-request@v3
        with:
          github-token: ${{ steps.generate-token.outputs.token }}
          message: |
            **⚠️ Module deletion detected ⚠️**

            Your pull reuquest contains the deletion of the following modules:
            ${{ steps.deleted-modules.outputs.result }}

            Please make sure that you have removed all references to the deleted modules in your code.
            You have to destroy the terragrunt also manually  from your local machine with the command:

            ```bash
            cd <module_path>
            with_sol_xxx terragrunt destroy
            ```

            Please post the output of the command in the comment section of this PR.

            ---
            🤖 I am a bot, beep beep.
            _(execution **${{ github.run_id }}** / attempt **${{ github.run_attempt }}**)_
          comment-tag: execution
