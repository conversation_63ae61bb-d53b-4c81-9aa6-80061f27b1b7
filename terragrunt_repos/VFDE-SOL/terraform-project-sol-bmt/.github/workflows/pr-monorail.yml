name: pr-monorail

on:
  pull_request:
    branches: [ "master" ]
    types: [ opened, synchronize, reopened ]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  monorail-pr:
    uses: VFDE-ISS/workflows/.github/workflows/monorail-pr.yaml@monorail-pr/v0
    if: github.repository == 'VFDE-SOL/template-terraform-project'
    # The use of a separate app is recommended but not necessary.
    # Removing the secrets will execute the PR comment using the workflow token.
    secrets:
      app_id: ${{ secrets.APP_SOL_MONORAIL_BOT_ID }}
      app_private_key: ${{ secrets.APP_SOL_MONORAIL_BOT_PRIVATE_KEY }}
