name: push-pre-commit

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  main:
    runs-on: self-hosted
    name: push-pre-commit
    if: >-
      github.event.pull_request.user.login != 'sol-bot[bot]' &&
      github.event.pull_request.user.login != 'sol-github-org-repos[bot]'
    steps:
      - name: Generate Token
        id: generate_token
        uses: VFDE-SOL/mirror-action-actions-create-github-app-token@v1
        with:
          app-id: ${{ secrets.APP_SOL_PRECOMMIT_ID }}
          private-key: ${{ secrets.APP_SOL_PRECOMMIT_PRIVATE_KEY }}

      - name: Run pre-commit-full
        uses: VFDE-SOL/actions/pre-commit-full@v1
        with:
          enable_kubernetes: false
          enable_terraform: true
          enable_terragrunt: true
          token: ${{ steps.generate_token.outputs.token }}
