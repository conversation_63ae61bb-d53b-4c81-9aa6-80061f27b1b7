# Requires policy bot installed - https://github.com/palantir/policy-bot

policy:
  approval:
    - required-checks-successful-base
    - required-checks-successful-atlantis
    - or:
        - dotfiles-changed
        - final-approval-for-template-sync
        - pre-commit-config-changed-by-bot
    - or:
        - and:
            - first-approval
            - final-approval
        - final-approval
        - silence-approval
        - fast-track-approval
        - approval-for-privatelink
        - docs-changed-by-bot
  disapproval:
    if:
      title:
        matches:
          - ^WIP
      has_labels:
        - lifecycle/rotten
        - lifecycle/stale
        - on hold
        - pr/do-not-merge/work-in-progress
        - wip
    requires:
      # WILL BE UPDATED BY gen-repo-bot-configs ACTION
      teams:
        - VFDE-SOL/team-batch-migration
        - VFDE-SOL/sol-admins
        - VFDE-SOL/team-tesla-tf-maintainer
approval_rules:
  # team based approvals --------------------------------------------------------------------------
  - name: first-approval
    description: contributors need first approval by own team by default. final approval is still necessary
    requires:
      count: 1
      # WILL BE UPDATED BY gen-repo-bot-configs ACTION
      teams:
        - VFDE-SOL/team-batch-migration
    options:
      allow_author: false
      allow_contributor: true
      invalidate_on_push: false
      request_review:
        enabled: true
        mode: teams
      methods:
        comments: []
        comment_patterns:
          - ^lgtm|LGTM$
          - '^\:\+1\: *$' # github 👍
          - ^\x{1F44D} *$ # unicode regex for 👍
  - name: final-approval
    description: contributors need final approval from a main-maintainer or silence-maintainer member
    requires:
      count: 1
      # WILL BE UPDATED BY gen-repo-bot-configs ACTION
      teams:
        - VFDE-SOL/team-batch-migration
    options:
      allow_author: false
      allow_contributor: true
      invalidate_on_push: false
      request_review:
        enabled: false # NOTE: manual step atm. The first-approval need to add the final-approval for the review.
        mode: teams
      methods:
        comments: []
        comment_patterns:
          - ^lgtm|LGTM$
          - '^\:\+1\: *$' # github 👍
          - ^\x{1F44D} *$ # unicode regex for 👍
  - name: final-approval-for-template-sync
    description: All changes by template sync require default approval from final-approval team
    if:
      only_has_contributors_in:
        users:
          - sol-github-org-repos[bot]
          - sol-bot[bot]
      has_author_in:
        users:
          - sol-github-org-repos[bot]
          - sol-bot[bot]
      author_is_only_contributor: false
      from_branch:
        pattern: ^chore/template_sync_.*
      title:
        matches:
          - '^chore\(sync-tpl-files\): upstream merge template repository$'
    requires:
      count: 1
      teams:
        - VFDE-SOL/team-batch-migration
    options:
      request_review:
        enabled: true # NOTE: manual step atm. The first-approval need to add the final-approval for the review.
        mode: teams
      methods:
        comments: []
        comment_patterns:
          - ^lgtm|LGTM$
          - '^\:\+1\: *$' # github 👍
          - ^\x{1F44D} *$ # unicode regex for 👍
  - name: silence-approval
    description: contributors need approval from a main-maintainer or silence-maintainer member (silence)
    requires:
      count: 1
      # WILL BE UPDATED BY gen-repo-bot-configs ACTION
      teams:
        - VFDE-SOL/sol-admins
        - VFDE-SOL/team-tesla-tf-maintainer
    options:
      allow_author: false
      allow_contributor: true
      invalidate_on_push: false
      request_review:
        enabled: false
      methods:
        comments: []
        comment_patterns:
          - ^lgtm|LGTM$
          - '^\:\+1\: *$' # github 👍
          - ^\x{1F44D} *$ # unicode regex for 👍
  - name: fast-track-approval
    description: fast-tracked teams can merge without approval
    if:
      only_has_contributors_in:
        # WILL BE UPDATED BY gen-repo-bot-configs ACTION
        teams:
          - VFDE-SOL/team-batch-migration
          - VFDE-SOL/sol-admins
          - VFDE-SOL/team-tesla-tf-maintainer
      has_author_in:
        # WILL BE UPDATED BY gen-repo-bot-configs ACTION
        teams:
          - VFDE-SOL/team-batch-migration
          - VFDE-SOL/sol-admins
          - VFDE-SOL/team-tesla-tf-maintainer
    requires:
      count: 1
      # WILL BE UPDATED BY gen-repo-bot-configs ACTION
      teams:
        - VFDE-SOL/team-batch-migration
        - VFDE-SOL/sol-admins
        - VFDE-SOL/team-tesla-tf-maintainer
    options:
      allow_author: true
      allow_contributor: true
      invalidate_on_push: true
      request_review:
        enabled: false
      methods:
        comments:
          - /self-approved
          - self-approved
  # file based approvals --------------------------------------------------------------------------
  - name: approval-for-privatelink
    description: additional approval option for privatelink components
    if:
      only_changed_files:
        paths:
          - .*/network/privatelink/.*
    requires:
      count: 1
      teams:
        - VFDE-SOL/team-tesla-tf-maintainer
        - VFDE-SOL/team-connectivity-service
    options:
      invalidate_on_push: true
      request_review:
        enabled: true
        mode: teams
      methods:
        comments: []
        comment_patterns:
          - ^lgtm|LGTM$
          - '^\:\+1\: *$' # github 👍
          - ^\x{1F44D} *$ # unicode regex for 👍
  - name: dotfiles-changed
    description: dotfiles changes require approval from sol-admin team
    if:
      changed_files:
        paths:
          - ^\..*
        ignore:
          - ^\.templateversionrc$
          - ^\.terraform.lock.hcl$
          - ^\.terragrunt-version$
          - ^\.tfswitchrc$
          - ^\.tgswitchrc$
    requires:
      count: 1
      teams:
        - VFDE-SOL/sol-admins
    options:
      allow_author: true
      allow_contributor: true
      invalidate_on_push: true
      request_review:
        enabled: true
        mode: teams
      methods:
        comments:
          - /self-approved
          - self-approved
  - name: docs-changed-by-bot
    description: CHANGELOG.md changes from sol-bot pass through
    if:
      changed_files:
        paths:
          - CHANGELOG.md
      only_has_contributors_in:
        users:
          - sol-bot[bot]
      has_author_in:
        users:
          - sol-bot[bot]
    requires:
      count: 0
    options:
      invalidate_on_push: false
      request_review:
        enabled: false
  - name: pre-commit-config-changed-by-bot
    description: pre-commit-config.yaml changes from sol-bot pass through
    if:
      only_changed_files:
        paths:
          - ^\.pre-commit-config.yaml$
      only_has_contributors_in:
        users:
          - sol-github-org-repos[bot]
      has_author_in:
        users:
          - sol-github-org-repos[bot]
    requires:
      count: 0
    options:
      invalidate_on_push: false
      request_review:
        enabled: false
  # miscellaneous approvals -----------------------------------------------------------------------
  - name: required-checks-successful-base
    description: all required checks are successful
    if:
      has_successful_status:
        - CommitChecker
        - pr-verify-commit-scopes
        - push-pre-commit
  - name: required-checks-successful-atlantis
    description: all required checks are successful
    if:
      has_successful_status:
        - atlantis/plan
