<p align="center">
  <img src="./.github/images/atlantis.png" alt="atlantis" width="100%">
</p>
<h1 style="font-size: 56px; margin: 0; padding: 0;" align="center" />
  terraform-project-sol-bmt
</h1>

<p align="center">
  <img src="https://github.vodafone.com/VFDE-SOL/terraform-project-sol-bmt/actions/workflows/push-pre-commit.yml/badge.svg" alt="pre-commit" />
  <img src="https://github.vodafone.com/VFDE-SOL/terraform-project-sol-bmt/actions/workflows/schedule-tf-check-for-changes.yml/badge.svg" alt="tf-check-for-changes" />
</p>

---

### Meta Information

```yaml
maintainer: VFDE-SOL/team-batch-migration

approvals:
  #first:
  #  - name: VFDE-SOL/team-tesla
  #    permission: push
  final:
    - name: VFDE-SOL/team-batch-migration
      permission: maintain
  silent:
    - name: VFDE-SOL/sol-admins
      permission: admin
    - name: VFDE-SOL/team-tesla-tf-maintainer
      permission: maintain
  #custom:
  #  - name: VFDE-SOL/team-connectivity-service
  #    permission: push

contacts:
  technical:
    team-batch-migration:
      email: <EMAIL>
      github: https://github.vodafone.com/orgs/VFDE-SOL/teams/team-batch-migration
      confluence: https://de.confluence.agile.vodafone.com/display/SOLMIG/04+Migration+Batch+Load+Team
    team-tesla:
      email: <EMAIL>
      github: https://github.vodafone.com/orgs/VFDE-SOL/teams/team-tesla/discussions
      confluence: https://de.confluence.agile.vodafone.com/pages/viewpage.action?spaceKey=SOLS&title=Tesla+-+Cloud
  business:
    email: <EMAIL>

related_accounts:
  - dev-bmt-sol-vfde

```

---

## About this repository

This repository organizes your terraform project in the following order

```
repo/
├─ env/
│  ├─ <env-name>/
│  │  ├─ <region-name>/
│  │  │  ├─ foundation/
├─ live/
│  ├─ application/
│  ├─ foundation/
│  ├─ network/
├─ modules/
│  ├─ foundation
```
The 3 key folders are `env`, `live` and `modules` which are used as:
* `env`: Calls terraform code from this location, this may refer terraform modules in the local repo or even external repos
* `live`: This contains terraform code local to this project, this can be used in when calling from `env` folder. This folder is further distinguished in 3 types on the basis of their use:
  * `application` - Application resources
  * `foundation` - Core resources (for example, TF state management)
  * `network` - Network resources
* `modules`: This folder contain terraform modules

## Know Issues

- terragrunt version >= 1.4.0 is currently unsupported [issue #25](https://github.vodafone.com/VFDE-SOL/template-terraform-project/issues/25)
  - see [terragrunt/issues/2542](https://github.com/gruntwork-io/terragrunt/issues/2542)
  - see [terraform/issues/32205](https://github.com/hashicorp/terraform/issues/32205)
  - see [terraform/v1.4/CHANGELOG](https://github.com/hashicorp/terraform/blob/v1.4/CHANGELOG.md#140-march-08-2023)
  - see [terraform/upgrade-guides](https://developer.hashicorp.com/terraform/language/upgrade-guides#provider-caching-during-terraform-init)
