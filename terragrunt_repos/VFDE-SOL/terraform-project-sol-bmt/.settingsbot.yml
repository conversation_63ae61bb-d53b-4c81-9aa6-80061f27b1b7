repository:
  description: Terraform project for sol-bmt
  homepage: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol-bmt
  has_wiki: false
  has_issues: true
  has_pages: false
  has_projects: false
  has_downloads: false
  is_template: false
  # Merge options: at least one must be true
  allow_rebase_merge: true
  allow_squash_merge: true
  allow_merge_commit: false
  allow_auto_merge: false
  delete_branch_on_merge: true
  # Extends logic:
  # Topics are always replaced, never merged with _extends.
  topics:
    - terraform-project
    - atlantis
    - settingsbot-managed
    - sol-bmt
branches:
  - name: master
    protection:
      allow_deletions: false
      allow_force_pushes: false
      required_pull_request_reviews:
        required_approving_review_count: 0
        dismiss_stale_reviews: false
        require_code_owner_reviews: false
        dismissal_restrictions:
          enabled: false
          teams: []
      required_status_checks:
        strict: true
        contexts:
          - CommitChecker
          - 'PolicyBot: master'
          - SettingsBot
          - pr-verify-commit-scopes
          - push-pre-commit
      enforce_admins: false
      required_conversation_resolution: true
      required_signed_commits: false
      required_linear_history: true
      restrictions:
        enabled: true
        apps:
          - sol-atlantis
        teams:
          - sol-admins
teams:
  - name: team-batch-migration
    permission: maintain
  - name: sol-admins
    permission: admin
  - name: team-tesla-tf-maintainer
    permission: maintain
    #custom:
    #  - name: VFDE-SOL/team-connectivity-service
    #    permission: push
