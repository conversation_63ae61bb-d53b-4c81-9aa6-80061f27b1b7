module "newtags" {
  source = "git::https://github.vodafone.com/VFDE-ISS/terraform-modules-base.git//modules/helper/mandatory_tags?ref=v0.0.6"

  project         = "SOL"
  environment     = local.account.attributes["vf_env"]
  security_zone   = "X1"
  managed_by      = "<EMAIL>"
  confidentiality = "C3"
}

data "aws_caller_identity" "current" {}

locals {
  account = module.accountindex.index.by_account_id[data.aws_caller_identity.current.account_id]
  ti_tag_map = {
    "Ti_TaggingVersion" = "2.0"
    "Ti_AppName"        = "sol"
    "Ti_BudgetId"       = "PO1502522145"
    "Ti_ManagedBy"      = "<EMAIL>",
  }
  combined_tags = merge(local.ti_tag_map, module.newtags.tags)
}
