# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(sol-bmt): update foundation/ssm_tasks to

terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol/tree/foundation/ssm_tasks/v0.4.0/modules/foundation/ssm_tasks
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol.git//modules/foundation/ssm_tasks?ref=foundation/ssm_tasks/v0.4.0"
}

prevent_destroy = true

dependency "tfremotestate" {
  config_path  = "../tfremotestate"
  skip_outputs = true
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}

dependency "notification" {
  config_path = "../notification"
}

include {
  path = find_in_parent_folders()
}

inputs = {
  sns_alarm_topic_arn = dependency.notification.outputs.topic_map["alarm-topic-arn"]

  tags = dependency.account_config.outputs.mandatory_tags
}
