terraform {
  source = "git::https://github.vodafone.com/VFDE-ISS/terraform-component-cloudtrail.git//?ref=v3.3.0"
}

prevent_destroy = true

dependency "tfremotestate" {
  config_path  = "../tfremotestate"
  skip_outputs = true
}

dependency "cloudtrail_access_log_bucket" {
  config_path = "../cloudtrail_access_log_bucket"
}

dependency "account_config" {
  config_path = "../account_config"
}

inputs = {
  prevent_delete = true

  s3_access_logging_enabled = true
  s3_access_logging_bucket  = dependency.cloudtrail_access_log_bucket.outputs.s3_access_logging_bucket_name

  event_selector = [{
    include_management_events : "true",
    read_write_type : "All"
    exclude_management_event_sources : [
      "kms.amazonaws.com",
      "rdsdata.amazonaws.com",
    ]
  }]

  tags = dependency.account_config.outputs.mandatory_tags

}

include {
  path = find_in_parent_folders()
}
