# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(dev-bmt): update foundation/notification to

terraform {
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol.git//modules/foundation/notification?ref=foundation/notification/v0.1.1"
}

prevent_destroy = true

dependency "tfremotestate" {
  config_path  = "../tfremotestate"
  skip_outputs = true
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}

include {
  path = find_in_parent_folders()
}

inputs = {
  project_prefix      = "sol"
  account_short_alias = dependency.account_config.outputs.account.short_alias
  tags                = dependency.account_config.outputs.mandatory_tags
}
