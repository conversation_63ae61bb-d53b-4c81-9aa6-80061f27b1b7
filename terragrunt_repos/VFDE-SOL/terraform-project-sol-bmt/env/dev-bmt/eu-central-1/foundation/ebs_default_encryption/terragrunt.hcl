# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(dev-bmt): update foundation/ebs_default_encryption to

terraform {
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol.git//modules/foundation/ebs_default_encryption?ref=foundation/ebs_default_encryption/v0.1.0"
}

prevent_destroy = true

dependency "tfremotestate" {
  config_path  = "../tfremotestate"
  skip_outputs = true
}

dependency "account_config" {
  config_path = "../account_config"
}

include {
  path = find_in_parent_folders()
}

inputs = {
  project_prefix = dependency.account_config.outputs.account.project
  mandatory_tags = dependency.account_config.outputs.mandatory_tags
}
