# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(dev-bmt): update foundation/aws_config to

terraform {
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol.git//modules/foundation/aws_config?ref=foundation/aws_config/v0.1.0"
}

prevent_destroy = true

dependency "tfremotestate" {
  config_path  = "../tfremotestate"
  skip_outputs = true
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}

include {
  path = find_in_parent_folders()
}

inputs = {
  mandatory_tags = dependency.account_config.outputs.mandatory_tags
}
