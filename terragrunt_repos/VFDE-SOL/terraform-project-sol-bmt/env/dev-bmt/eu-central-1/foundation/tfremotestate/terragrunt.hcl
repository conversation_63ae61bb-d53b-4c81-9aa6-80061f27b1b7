terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-component-tfremotestate/tree/v0.2.1/
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-component-tfremotestate.git//?ref=v0.2.1"
}


include {
  path = find_in_parent_folders()
}

dependency "account_config" {
  config_path = "../account_config"
}

inputs = {
  tags = merge(dependency.account_config.outputs.mandatory_tags, {
    "CET_TerraformSource" = "VFDE-SOL/terraform-component-tfremotestate",
  })
}
