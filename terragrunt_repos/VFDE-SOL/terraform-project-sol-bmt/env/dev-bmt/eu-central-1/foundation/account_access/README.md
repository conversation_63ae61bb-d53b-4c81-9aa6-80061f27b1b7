# Terraform Module for Permissions Management (WiP)

This Terraform module is designed to manage permissions. Due to the sensitive nature of the permissions being managed, this module is handled in a dedicated repository for security reasons.

Please visit the [iam-sol-bmt](https://github.vodafone.com/VFDE-SOL/iam-sol-bmt) repository to access this module and its documentation.

We take security seriously and have taken extra precautions to ensure that this module is managed in a secure and responsible manner. As such, we require that all contributions and issues related to this module be handled through the dedicated repository.


**NOTE: The dedicate repository is not completed, but the Terraform state are stored following the standard of this one
