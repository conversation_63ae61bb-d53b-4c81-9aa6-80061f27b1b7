# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(dev-bmt): update foundation/cicd_support to

terraform {
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol.git//modules/foundation/cicd_support?ref=foundation/cicd_support/v0.1.0"
}

prevent_destroy = true

include {
  path = find_in_parent_folders()
}

dependency "tfremotestate" {
  config_path  = "../tfremotestate"
  skip_outputs = true
}

dependency "account_config" {
  config_path = "../account_config"
}

inputs = {
  tags = dependency.account_config.outputs.mandatory_tags
}
