# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(dev-bmt): update foundation/session_manager to

terraform {
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol.git//modules/foundation/session_manager?ref=foundation/session_manager/v0.1.0"
}

prevent_destroy = false

dependency "tfremotestate" {
  config_path  = "../tfremotestate"
  skip_outputs = true
}

dependency "account_config" {
  config_path = "../account_config"
}

include {
  path = find_in_parent_folders()
}

inputs = {
  tags                  = dependency.account_config.outputs.mandatory_tags
  s3_bucket_key_enabled = true
}
