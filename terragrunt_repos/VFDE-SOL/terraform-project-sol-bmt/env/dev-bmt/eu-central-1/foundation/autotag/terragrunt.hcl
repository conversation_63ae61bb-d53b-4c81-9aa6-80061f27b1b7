terraform {
  source = "git::https://github.vodafone.com/VFDE-ISS/terraform-component-autotag.git//?ref=v1.1.0"
}

prevent_destroy = true

dependency "tfremotestate" {
  config_path  = "../tfremotestate"
  skip_outputs = true
}

dependency "cloudtrail" {
  config_path  = "../cloudtrail"
  skip_outputs = true
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}

inputs = {


  tags = dependency.account_config.outputs.mandatory_tags
  autotags = merge(dependency.account_config.outputs.mandatory_tags,
    {
      "TaggedBy" = "component-autotag",
  })
  autotag_eni_flag = true
  autotag_ebs_flag = true

}

include {
  path = find_in_parent_folders()
}
