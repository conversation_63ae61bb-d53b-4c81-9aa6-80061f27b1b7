# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(dev-bmt): update foundation/datadog to

terraform {
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol.git//modules/foundation/datadog?ref=foundation/datadog/v0.5.0"
}

include {
  path = find_in_parent_folders()
}

dependency "tfremotestate" {
  config_path  = "../../foundation/tfremotestate"
  skip_outputs = true
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}

inputs = {
  tags                = dependency.account_config.outputs.mandatory_tags
  account_short_alias = dependency.account_config.outputs.account.short_alias

  datadog_logindex = "vfde-logindex-nonprod-3d"

  # requested by CS to tackle datadog cost explosion on 2023
  create_datadog_aws_integration          = false
  create_firehose_delivery_stream_metrics = false

  ti_appname   = "bmt"
  ti_managedby = "<EMAIL>"
}
