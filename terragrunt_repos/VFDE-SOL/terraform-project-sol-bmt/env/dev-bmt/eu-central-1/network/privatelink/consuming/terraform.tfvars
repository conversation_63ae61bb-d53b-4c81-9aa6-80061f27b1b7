# Deploymemnt guide with configuration examples: https://github.vodafone.com/pages/VFDE-SOL/docs-sol-cet/docs/developer_guide/technical_solutions/privatelink_guide.html#consuming-endpoints

connected_account_domain_names = [
  "internal.vodafone.com",
  "mgmt.sol-vf.de",
  "deliveryzone.vodafone.com"
]
vodafone_endpoints = {
  vf_deployvip   = { deploy = true },
  vf_trend_micro = { deploy = true },
  vf_smtp_relay  = { deploy = false },
  eks_tools_mgmt = { deploy = false }
}

consuming_endpoints = {

  #  deploy-aws
  deploy_aws_ep = {
    deploy       = true
    service_name = "com.amazonaws.vpce.eu-central-1.vpce-svc-058c85c96ee0e9430"
    security_groups = {
      ingress = [{
        from_port   = 443
        to_port     = 443
        protocol    = "tcp"
        cidr_blocks = "vpc_cidrs"
        },
        {
          from_port   = 5000
          to_port     = 5002
          protocol    = "tcp"
          cidr_blocks = "vpc_cidrs"
      }],
      # egress = []
    }
    dns = [
      {
        id        = "deploy-aws"
        zone_name = "internal.vodafone.com"
        name      = "deploy-aws"
      },
      {
        id        = "secondary"
        zone_name = "deliveryzone.vodafone.com"
        name      = "vf-aws-access"
      },
    ]
  },

}
