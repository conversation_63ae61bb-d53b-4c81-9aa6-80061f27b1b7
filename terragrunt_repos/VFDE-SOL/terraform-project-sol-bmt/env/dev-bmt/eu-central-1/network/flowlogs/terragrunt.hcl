# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(dev-bmt): update network/flowlogs to

terraform {
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol.git//modules/network/flowlogs?ref=network/flowlogs/v0.0.1"
}

prevent_destroy = true

include {
  path = find_in_parent_folders()
}

dependency "vpc" {
  config_path = "../vpc"
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}

dependency "tfremotestate" {
  config_path  = "../../foundation/tfremotestate"
  skip_outputs = true
}


inputs = {
  # Optional inputs
  # project_prefix = "sol"
  mandatory_tags = dependency.account_config.outputs.mandatory_tags
  # Mandatory Inputs
  vpc_id = dependency.vpc.outputs.vpc_id
}
