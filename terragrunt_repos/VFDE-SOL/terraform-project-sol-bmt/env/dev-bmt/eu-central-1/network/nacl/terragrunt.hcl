# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(dev-bmt): update network/nacl to

terraform {
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol.git//modules/network/nacl?ref=network/nacl/v0.1.0"
}

prevent_destroy = true

include {
  path = find_in_parent_folders()
}

dependency "vpc" {
  config_path = "../vpc"
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}

dependency "tfremotestate" {
  config_path  = "../../foundation/tfremotestate"
  skip_outputs = true
}

inputs = {
  vpc_id             = dependency.vpc.outputs.vpc_id
  private_subnet_ids = dependency.vpc.outputs.subnetgroup_private.subnets
  public_subnet_ids  = dependency.vpc.outputs.subnetgroup_public.subnets
  eks_subnet_ids     = dependency.vpc.outputs.subnetgroup_eks.subnets
  tags               = dependency.account_config.outputs.mandatory_tags
}
