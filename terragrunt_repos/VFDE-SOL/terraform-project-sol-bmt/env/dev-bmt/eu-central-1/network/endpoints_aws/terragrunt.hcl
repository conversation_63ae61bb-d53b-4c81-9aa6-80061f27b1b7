# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(dev-bmt): update network/endpoints_aws to

terraform {
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol.git//modules/network/endpoints_aws?ref=network/endpoints_aws/v0.1.0"
}

prevent_destroy = true

include {
  path = find_in_parent_folders()
}

dependency "vpc" {
  config_path = "../vpc"
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}

dependency "tfremotestate" {
  config_path  = "../../foundation/tfremotestate"
  skip_outputs = true
}

inputs = {
  # additional_endpoints = {}
  vpc_id          = dependency.vpc.outputs.vpc_id
  vpc_cidr        = dependency.vpc.outputs.vpc_cidr
  route_table_ids = concat(dependency.vpc.outputs.subnetgroup_private.route_tables, dependency.vpc.outputs.subnetgroup_eks.route_tables, dependency.vpc.outputs.subnetgroup_public.route_tables)
  subnet_ids      = dependency.vpc.outputs.subnetgroup_private.subnets
  tags            = dependency.account_config.outputs.mandatory_tags
}
