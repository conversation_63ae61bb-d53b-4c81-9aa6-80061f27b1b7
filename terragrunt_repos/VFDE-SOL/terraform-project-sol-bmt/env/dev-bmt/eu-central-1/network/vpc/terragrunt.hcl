# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(dev-bmt): update network/vpc to

terraform {
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol.git//modules/network/vpc?ref=network/vpc/v0.1.0"
}

prevent_destroy = true

include {
  path = find_in_parent_folders()
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}

dependency "tfremotestate" {
  config_path  = "../../foundation/tfremotestate"
  skip_outputs = true
}

inputs = {
  cidr_segment = 115
  enable_eks   = false
  tags         = dependency.account_config.outputs.mandatory_tags
}
