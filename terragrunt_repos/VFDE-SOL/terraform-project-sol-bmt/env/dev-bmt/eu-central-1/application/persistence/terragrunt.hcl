# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(dev-bmt): update foundation/iam_roles to

terraform {
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol-bmt.git//modules/persistence?ref=persistence/v0.2.2"
}

prevent_destroy = true

include {
  path = find_in_parent_folders()
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}

dependency "account_index_mgmt" {
  config_path = "../../foundation/account_index_mgmt"
}

dependency "dns" {
  config_path = "../../network/dns"
}

dependency "vpc" {
  config_path = "../../network/vpc"
}

dependency "notification" {
  config_path = "../../foundation/notification"
}

dependency "session_manager" {
  config_path = "../../foundation/session_manager"
}

inputs = {
  project_prefix         = "sol"
  mgmt_account_id        = dependency.account_index_mgmt.outputs.index.by_short_alias.mgmt.account_id
  private_hosted_zone_id = dependency.dns.outputs.internal_private.zone_id

  vpc_id             = dependency.vpc.outputs.vpc_id
  vpc_cidr           = dependency.vpc.outputs.vpc_cidr
  private_subnet_ids = dependency.vpc.outputs.subnetgroup_private.subnets

  alarm_sns_topic = dependency.notification.outputs.topic_map.alarm-topic-arn

  ssm_iam_policy_arn = dependency.session_manager.outputs.iam_policy_arn

  mandatory_tags = dependency.account_config.outputs.mandatory_tags
}
