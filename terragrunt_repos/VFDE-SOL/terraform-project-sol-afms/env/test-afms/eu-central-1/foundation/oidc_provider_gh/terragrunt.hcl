# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(prod-tdm): update foundation/cloudwatch to

terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol-afms/tree/oidc_provider_gh/v0.1.0/modules/oidc_provider_gh
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol-afms.git//modules/oidc_provider_gh?ref=oidc_provider_gh/v0.1.0"
}

prevent_destroy = true

dependency "tfremotestate" {
  config_path  = "../tfremotestate"
  skip_outputs = true
}

dependency "account_config" {
  config_path = "../account_config"
}

include {
  path = find_in_parent_folders()
}

inputs = {
  tags = dependency.account_config.outputs.mandatory_tags
}
