terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol/tree/persistence/s3_access_log_bucket/v0.1.0/modules/persistence/s3_access_log_bucket
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol.git//modules/persistence/s3_access_log_bucket?ref=persistence/s3_access_log_bucket/v0.1.0"
}

prevent_destroy = true

include {
  path = find_in_parent_folders()
}

dependency "tfremotestate" {
  config_path  = "../tfremotestate"
  skip_outputs = true
}

dependency "account_config" {
  config_path = "../account_config"
}

inputs = {
  name_suffix = "cloudtrail-access-logs"

  expiration_in_days    = 1
  expiration_nc_in_days = 1

  tags = dependency.account_config.outputs.mandatory_tags

}
