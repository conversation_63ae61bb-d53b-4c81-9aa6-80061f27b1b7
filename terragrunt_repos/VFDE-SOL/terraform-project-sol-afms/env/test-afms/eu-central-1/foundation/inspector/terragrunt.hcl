# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(<REPLACE_WITH_ENV_NAME>): update foundation/inspector to

# terraform {
# Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol/tree/foundation/inspector/v0.0.1/modules/foundation/inspector
#   source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol.git//modules/foundation/inspector?ref=foundation/inspector/v0.0.1"
# }

# prevent_destroy = true

# dependency "tfremotestate" {
#   config_path  = "../tfremotestate"
#   skip_outputs = true
# }

# dependency "account_config" {
#   config_path = "../../foundation/account_config"
# }

# include {
#   path = find_in_parent_folders()
# }

# inputs = {
#   account_ids = [
#     dependency.account_config.outputs.account.account_id
#     ]
#     resource_types = [
#         "ECR",
#         "EC2",
#         "LAMBDA",
#     ]
# }

# Actually outcommented as a bug in aws provider does not allow planing inspector resources
# https://github.com/hashicorp/terraform-provider-aws/issues/27639
