# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(dev-afms): update foundation/iam_roles to

terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol/tree/foundation/iam_roles/v0.18.0/modules/foundation/iam_roles
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol.git//modules/foundation/iam_roles?ref=foundation/iam_roles/v0.18.0&depth=1"

}

prevent_destroy = true

include {
  path = find_in_parent_folders()
}

dependency "tfremotestate" {
  config_path  = "../tfremotestate"
  skip_outputs = true
}

dependency "account_config" {
  config_path = "../account_config"
}

dependency "session_manager" {
  config_path = "../session_manager"
}

inputs = {
  enable_role_cloudcraft = false
  session_manager_policy = dependency.session_manager.outputs.iam_connect_policy_arn
  tags                   = dependency.account_config.outputs.mandatory_tags
}
