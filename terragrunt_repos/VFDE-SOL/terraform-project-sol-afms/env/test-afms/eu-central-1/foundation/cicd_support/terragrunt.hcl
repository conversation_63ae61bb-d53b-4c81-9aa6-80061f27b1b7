# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(dev-afms): update foundation/cicd_support to

terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol/tree/foundation/cicd_support/v0.2.1/modules/foundation/cicd_support
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol.git//modules/foundation/cicd_support?ref=foundation/cicd_support/v0.2.1"
}

prevent_destroy = true

include {
  path = find_in_parent_folders()
}

dependency "tfremotestate" {
  config_path  = "../tfremotestate"
  skip_outputs = true
}

dependency "account_config" {
  config_path = "../account_config"
}

inputs = {
  tags = dependency.account_config.outputs.mandatory_tags

  projects            = ["afms"]
  enable_local_policy = true
}
