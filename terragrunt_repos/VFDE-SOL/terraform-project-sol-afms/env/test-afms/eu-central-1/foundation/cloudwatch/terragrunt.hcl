# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(prod-tdm): update foundation/cloudwatch to

terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol/tree/foundation/cloudwatch/v0.3.1/modules/foundation/cloudwatch
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol.git//modules/foundation/cloudwatch?ref=foundation/cloudwatch/v0.3.1"
}

prevent_destroy = true

dependency "tfremotestate" {
  config_path  = "../tfremotestate"
  skip_outputs = true
}

dependency "account_config" {
  config_path = "../account_config"
}

include {
  path = find_in_parent_folders()
}

inputs = {
  mandatory_tags = dependency.account_config.outputs.mandatory_tags
  short_alias    = dependency.account_config.outputs.account.short_alias
}
