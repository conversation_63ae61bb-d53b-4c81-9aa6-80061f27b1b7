# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(<REPLACE_WITH_ENV_NAME>): update foundation/config to

terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol/tree/foundation/config/v0.2.0/modules/foundation/config
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol.git//modules/foundation/config?ref=foundation/config/v0.2.0"
}

prevent_destroy = true

dependency "tfremotestate" {
  config_path  = "../tfremotestate"
  skip_outputs = true
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}

include {
  path = find_in_parent_folders()
}

inputs = {
  account_short_alias = dependency.account_config.outputs.account.short_alias
  account_alias       = dependency.account_config.outputs.account.alias
  tags                = dependency.account_config.outputs.mandatory_tags
}
