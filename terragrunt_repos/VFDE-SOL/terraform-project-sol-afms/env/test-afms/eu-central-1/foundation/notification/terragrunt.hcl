# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(dev-afms): update foundation/notification to

terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol/tree/foundation/notification/v0.2.2/modules/foundation/notification
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol.git//modules/foundation/notification?ref=foundation/notification/v0.2.2"
}

prevent_destroy = true

dependency "tfremotestate" {
  config_path  = "../tfremotestate"
  skip_outputs = true
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}

include {
  path = find_in_parent_folders()
}

locals {
  subscribers_all_topic = []
}

inputs = {
  project_prefix      = "sol"
  account_short_alias = dependency.account_config.outputs.account.short_alias
  subscribers = {
    info-topic  = local.subscribers_all_topic,
    warn-topic  = local.subscribers_all_topic,
    alarm-topic = ["<EMAIL>"]
  }
  tags = dependency.account_config.outputs.mandatory_tags
}
