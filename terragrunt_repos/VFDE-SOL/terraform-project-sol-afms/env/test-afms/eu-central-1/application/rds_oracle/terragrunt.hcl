terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol-afms/tree/rds_oracle/v0.2.8/modules/rds_oracle
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol-afms.git//modules/rds_oracle?ref=rds_oracle/v0.2.8"
}

include {
  path = find_in_parent_folders()
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}

dependency "bh" {
  config_path = "../bastion_host"
}

dependency "vpc" {
  config_path = "../../network/vpc"
}

dependency "eks" {
  config_path = "../../platform/cet_eks/"
}

dependency "cicd_support" {
  config_path = "../../foundation/cicd_support"
}

inputs = {
  name                  = "afmsdb"
  master_db_username    = "adminuser"
  instance_class        = "db.m5.2xlarge"
  allocated_storage     = 30
  max_allocated_storage = 1000
  monitoring_interval   = 60
  subnet_ids            = dependency.vpc.outputs.subnetgroup_private.subnets

  performance_insights_enabled    = true
  enabled_cloudwatch_logs_exports = ["audit", "trace", "listener", "alert"]

  tags                    = merge(dependency.account_config.outputs.mandatory_tags, { "rds-autoscheduler" = "enabled" })
  allowed_security_groups = [dependency.eks.outputs.pod_security_group_id, dependency.bh.outputs.security_group_id]

  policy_arns = [
    dependency.cicd_support.outputs.delivery_buckets.afms.local_ro_policy,
    dependency.cicd_support.outputs.delivery_buckets.afms.local_rw_policy
  ]


}
