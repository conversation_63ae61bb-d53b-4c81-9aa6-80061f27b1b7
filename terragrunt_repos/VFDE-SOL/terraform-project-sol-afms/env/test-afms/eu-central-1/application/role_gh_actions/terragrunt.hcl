# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(prod-tdm): update foundation/cloudwatch to

terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol-afms/tree/oidc_role_gh/v0.1.3/modules/oidc_role_gh
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol-afms.git//modules/oidc_role_gh?ref=oidc_role_gh/v0.1.3"
}

prevent_destroy = true

dependency "tfremotestate" {
  config_path  = "../../foundation/tfremotestate"
  skip_outputs = true
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}

dependency "account_mgmt" {
  config_path = "../../foundation/account_index_mgmt"
}

dependency "oidc_provider" {
  config_path = "../../foundation/oidc_provider_gh"
}

dependency "rds_oracle" {
  config_path = "../../application/rds_oracle"
}

dependency "session_manager" {
  config_path = "../../foundation/session_manager"
}

dependency "cicd_support" {
  config_path = "../../foundation/cicd_support"
}

include {
  path = find_in_parent_folders()
}

inputs = {
  name          = "app-cicd"
  oidc_provider = dependency.oidc_provider.outputs.arn
  repository_list = [
    "VFDE-AFMS/gh-action-testbench:*",
    "VFDE-AFMS/deploy-afms-app:*"
  ]

  policy_arns = [
    dependency.session_manager.outputs.iam_connect_policy_arn,
    dependency.rds_oracle.outputs.policy.ssm_read_only.arn,
    dependency.cicd_support.outputs.delivery_buckets.afms.local_ro_policy,
  ]

  custom_policy = [{
    sid       = "AllowGetTags"
    effect    = "Allow"
    actions   = ["ec2:DescribeTags"]
    resources = ["*"]
  }]

  # To get read permissions on delivery bucket, in another account
  arn_delivery_bucket        = "arn:aws:s3:::${dependency.account_mgmt.outputs.index.by_short_alias.mgmt.account_id}-delivery-afms"
  account_id_delivery_bucket = dependency.account_mgmt.outputs.index.by_short_alias.mgmt.account_id

  tags = dependency.account_config.outputs.mandatory_tags
}
