# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(dev): update application/ami-consumer to


terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-component-ami-builder/tree/v0.5.0/consumer
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-component-ami-builder.git//consumer?ref=v0.5.0"
}

include {
  path = find_in_parent_folders()
}

dependency "tfremotestate" {
  config_path  = "../../foundation/tfremotestate"
  skip_outputs = true
}

// dependency "account_config" {
//   config_path = "../../foundation/account_config"
// }

inputs = {
  # tags       = dependency.account_config.outputs.mandatory_tags
  kms_key_id = "arn:aws:kms:eu-central-1:************:key/9b59ce60-9801-4b5d-87c7-d8d00e87a9a6"
}
