terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol-afms/tree/data_transfer/v0.1.0/modules/data_transfer
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol-afms.git//modules/data_transfer?ref=data_transfer/v0.1.0"
}

include {
  path = find_in_parent_folders()
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}

dependency "vpc" {
  config_path = "../../network/vpc"
}

dependency "bastion_host" {
  config_path = "../../application/bastion_host"
}

dependency "cet_eks" {
  config_path = "../../platform/cet_eks"

  # substitute this with actual dependency output, once updated to >v0.8.0
  # v0.7.0 cet_eks does not have SecGroup as output
  skip_outputs = true
  mock_outputs = {
    node_additional_security_group_id = "sg-080b8fced2c1751e2"
  }
}

dependency "notification" {
  config_path = "../../foundation/notification"
}

inputs = {
  vpc_id            = dependency.vpc.outputs.vpc_id
  lambda_subnet_ids = dependency.vpc.outputs.subnetgroup_private.subnets
  efs_allow_security_group_ids = [
    dependency.bastion_host.outputs.security_group_id,
    dependency.cet_eks.outputs.node_additional_security_group_id
  ]
  alarms_arn_actions_list = [dependency.notification.outputs.topic_map["alarm-topic-arn"]]

  disable_kms_cmk = true # Receiving bucket

  allow_cross_account = ["arn:aws:iam::************:role/airflow-role"] # Gigabeam
  tags                = dependency.account_config.outputs.mandatory_tags
}
