terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol-afms/tree/asg_based/v0.4.0/modules/asg_based
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol-afms.git//modules/asg_based?ref=asg_based/v0.4.0"
}

include {
  path = find_in_parent_folders()
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}

dependency "account_mgmt" {
  config_path = "../../foundation/account_index_mgmt"
}

dependency "vpc" {
  config_path = "../../network/vpc"
}

dependency "ssm_tasks" {
  config_path = "../../foundation/ssm_tasks"
}

dependency "session_manager" {
  config_path = "../../foundation/session_manager"
}

dependency "directory_service" {
  config_path = "../../application/directory_service"
}

dependency "cicd_support" {
  config_path = "../../foundation/cicd_support"
}

dependency "oracle" {
  config_path = "../../application/rds_oracle"
}

dependency "dns" {
  config_path = "../../network/dns"
}

dependency "alb" {
  config_path = "../../network/public_alb_github_auth"
}

dependency "ssm_params_app" {
  config_path = "../../application/k8s/app_params"
}

inputs = {
  name           = "gui"
  install_fv     = true
  project_prefix = dependency.account_config.outputs.account.project
  tags           = dependency.account_config.outputs.mandatory_tags

  ami_filter = "CIS-WIN2019-PCS-afms-web*"

  ami_source_account_id = dependency.account_mgmt.outputs.index.by_short_alias.mgmt.account_id # When using an AMI owned by this very same AWS account: dependency.account_config.outputs.account.account_id

  ec2_type             = "t3.2xlarge"
  asg_desired_capacity = 1

  scheduled_scaling_enabled = false
  scheduled_scaling = {
    downscale_cron = "30 17 * * 1-7"
    upscale_cron   = "30 03 * * 1-7"
  }

  root_volume_size = 200
  data_disk_size   = 300

  subnet_ids = dependency.vpc.outputs.subnetgroup_private.subnets

  allowed_egress_rules_with_cidrs = {
    http-80-tcp         = ["0.0.0.0/0"]
    https-443-tcp       = ["0.0.0.0/0"]
    mssql-tcp           = dependency.vpc.outputs.subnetgroup_private.cidrs
    mssql-analytics-tcp = dependency.vpc.outputs.subnetgroup_private.cidrs

    all-all = [for ip in dependency.directory_service.outputs.dns_ip_addresses : format("%s/32", ip)]
  }

  allowed_ingress_rules_with_security_group_ids = {
    http-80-tcp = [dependency.alb.outputs.security_group_id_map["public-alb-github-auth-websrv"]] // Health check from load-balancer
  }

  ssm_tasks_output_bucket                   = dependency.ssm_tasks.outputs.output_bucket_name
  ssm_tasks_alarm_topic_arn                 = dependency.ssm_tasks.outputs.sns_alarm_trigger_topic_arn
  ssm_session_manager_iam_policy_arn        = dependency.session_manager.outputs.iam_policy_arn
  ssm_tasks_output_bucket_access_policy_arn = dependency.ssm_tasks.outputs.output_bucket_access_policy_arn


  directory_id       = dependency.directory_service.outputs.id
  start_cw_log_agent = true

  additional_iam_policies = [
    dependency.cicd_support.outputs.delivery_buckets.afms.local_ro_policy, // iam role for s3 bucket access
    dependency.oracle.outputs.policy.ssm_read_only.arn,                    // oracle KMS decrypt,encrypt
    dependency.ssm_params_app.outputs.policy.ssm_read_only.arn             // ssm paramter store policy
  ]

  s3_name               = dependency.cicd_support.outputs.delivery_buckets.afms.name                                    // s3 bucket name                                                        // s3 bucket name
  dns_pub_domain_name   = dependency.dns.outputs.pub.domain_name                                                        // public dns domain name
  alb_target_group_arns = [dependency.alb.outputs.albs_target_group_arns["public-alb-github-auth-websrv"]["ip"]["arn"]] // alb target group
  security_groups       = [dependency.oracle.outputs.client_security_group_id]                                          // add 2nd security group - oracleclient

}
