# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(prod-tdm): update foundation/cloudwatch to

terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol-afms/tree/irsa_role_k8s/v0.2.0/modules/irsa_role_k8s
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol-afms.git//modules/irsa_role_k8s?ref=irsa_role_k8s/v0.2.0"
}

prevent_destroy = true

dependency "tfremotestate" {
  config_path  = "../../../foundation/tfremotestate"
  skip_outputs = true
}

dependency "account_config" {
  config_path = "../../../foundation/account_config"
}

dependency "cicd_support" {
  config_path = "../../../foundation/cicd_support"
}

dependency "cet_eks" {
  config_path = "../../../platform/cet_eks"
}

dependency "rds_oracle" {
  config_path = "../../../application/rds_oracle"
}

dependency "app_params" {
  config_path = "../../../application/k8s/app_params"
}

dependency "data_transfer" {
  config_path = "../../../application/data_transfer"
}

include {
  path = find_in_parent_folders()
}

inputs = {
  cluster_name              = dependency.cet_eks.outputs.cluster_name
  service_account_name      = "afms-app"
  service_account_namespace = "afms-app"

  policy_arns = [
    dependency.rds_oracle.outputs.policy.ssm_read_only.arn,
    dependency.app_params.outputs.policy.ssm_read_only.arn,
    dependency.cicd_support.outputs.delivery_buckets.afms.local_ro_policy,
    dependency.data_transfer.outputs.policy.read_write.arn,
  ]

  tags = dependency.account_config.outputs.mandatory_tags
}
