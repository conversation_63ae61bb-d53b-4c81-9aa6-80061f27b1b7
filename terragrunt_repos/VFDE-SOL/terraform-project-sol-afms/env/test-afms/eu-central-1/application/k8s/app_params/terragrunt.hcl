terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol-afms/tree/app_params/v0.3.0/modules/app_params
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol-afms//modules/app_params?ref=app_params/v0.3.0"
}

include {
  path = find_in_parent_folders()
}

prevent_destroy = false

dependency "tfremotestate" {
  config_path  = "../../../foundation/tfremotestate"
  skip_outputs = true
}

dependency "account_config" {
  config_path = "../../../foundation/account_config"
}

dependency "dns" {
  config_path = "../../../network/dns"
}

dependency "oracle" {
  config_path = "../../../application/rds_oracle"
}


inputs = {
  svc_domain = join(".", [
    "svc",
    dependency.dns.outputs.internal_private.domain_name
  ])
  db_endpoint    = dependency.oracle.outputs.endpoint
  db_servicename = dependency.oracle.outputs.db_name
  kafka_server   = "cdh-cim.xtest2.x-test14.aws.solstice.vodafone.com:9000"

  tags = dependency.account_config.outputs.mandatory_tags
}
