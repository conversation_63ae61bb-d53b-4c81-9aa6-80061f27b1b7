terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-component-bastionhost/tree/v0.5.2
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-component-bastionhost.git?ref=v0.5.2"
}

include {
  path = find_in_parent_folders()
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}

dependency "account_mgmt" {
  config_path = "../../foundation/account_index_mgmt"
}


dependency "vpc" {
  config_path = "../../network/vpc"
}

dependency "notification" {
  config_path = "../../foundation/notification"
}

dependency "ssm_tasks" {
  config_path = "../../foundation/ssm_tasks"
}

dependency "session_manager" {
  config_path = "../../foundation/session_manager"
}

dependency "cicd_support" {
  config_path = "../../foundation/cicd_support"
}

inputs = {
  project_prefix = dependency.account_config.outputs.account.project
  tags           = dependency.account_config.outputs.mandatory_tags

  ami_filter            = "CIS-AMZN2-PCS-2025*"
  ami_source_account_id = dependency.account_mgmt.outputs.index.by_short_alias.mgmt.account_id # When using an AMI owned by this very same AWS account: dependency.account_config.outputs.account.account_id

  ssm_tasks_output_bucket_access_policy_arn = dependency.ssm_tasks.outputs.output_bucket_access_policy_arn
  ssm_tasks_output_bucket                   = dependency.ssm_tasks.outputs.output_bucket_name
  ssm_tasks_alarm_topic_arn                 = dependency.notification.outputs.topic_map.info-topic-arn
  ssm_session_manager_iam_policy_arn        = dependency.session_manager.outputs.iam_policy_arn
  alarm_sns_topic_arn                       = dependency.notification.outputs.topic_map.info-topic-arn

  addional_iam_policies = [dependency.cicd_support.outputs.delivery_buckets["afms"]["local_ro_policy"]]

  vpc_id             = dependency.vpc.outputs.vpc_id
  private_subnet_ids = dependency.vpc.outputs.subnetgroup_private.subnets
}
