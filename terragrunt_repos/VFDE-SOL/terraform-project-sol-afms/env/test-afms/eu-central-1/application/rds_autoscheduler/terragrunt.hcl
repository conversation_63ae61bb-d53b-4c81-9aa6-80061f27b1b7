terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-component-rds-autoscheduler/tree/v0.2.1
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-component-rds-autoscheduler.git?ref=v0.2.1"
}

include {
  path = find_in_parent_folders()
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}


inputs = {
  tags                                        = dependency.account_config.outputs.mandatory_tags
  rds_autoscheduler_tag_key                   = "rds-autoscheduler"
  rds_autoscheduler_tag_value                 = "enabled"
  rds_autoscheduler_schedule_expression_start = "cron(0 2 ? * MON-FRI *)"
  rds_autoscheduler_schedule_expression_stop  = "cron(0 19 ? * MON-FRI *)"
}
