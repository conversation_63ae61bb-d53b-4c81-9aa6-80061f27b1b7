terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol-afms/tree/directory_service/v0.3.0/modules/directory_service
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol-afms.git//modules/directory_service?ref=directory_service/v0.3.0"
}

include {
  path = find_in_parent_folders()
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}


dependency "vpc" {
  config_path = "../../network/vpc"
}

dependency "dns" {
  config_path = "../../network/dns"
}

dependency "account_index_mgmt" {
  config_path = "../../foundation/account_index_mgmt"
}

inputs = {
  project_prefix = dependency.account_config.outputs.account.project
  tags           = dependency.account_config.outputs.mandatory_tags

  fqdn                   = "ds.${dependency.dns.outputs.internal_private.domain_name}"
  private_hosted_zone_id = dependency.dns.outputs.internal_private.zone_id

  vpc_id     = dependency.vpc.outputs.vpc_id
  subnet_ids = chunklist(dependency.vpc.outputs.subnetgroup_private.subnets, 2)[0]

  lambda_layers_arn = {
    autorotate_ds = "arn:aws:lambda:eu-central-1:${dependency.account_index_mgmt.outputs.index.by_short_alias.mgmt.account_id}:layer:autorotate_ds:1"
  }
}
