# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(prod-afms): update platform/cet_eks to

terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-component-eks/tree/cet-eks/v1.0.1/modules/cet-eks
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-component-eks.git//modules/cet-eks?ref=cet-eks/v1.0.1"
}

prevent_destroy = true

include {
  path = find_in_parent_folders()
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}

dependency "iam_roles" {
  config_path = "../../foundation/iam_roles"
}

dependency "vpc" {
  config_path = "../../network/vpc"
}

inputs = {
  tags = dependency.account_config.outputs.mandatory_tags

  k8s_version = "1.30"

  vpc_id = dependency.vpc.outputs.vpc_id

  subnetgroup_eks_pods   = dependency.vpc.outputs.subnetgroup_eks
  subnetgroup_eks_nodes  = dependency.vpc.outputs.subnetgroup_eks_node
  subnetgroup_eks_master = dependency.vpc.outputs.subnetgroup_eks_master

  role_eks_admin_arn = dependency.iam_roles.outputs.role_eks_admin_arn
}
