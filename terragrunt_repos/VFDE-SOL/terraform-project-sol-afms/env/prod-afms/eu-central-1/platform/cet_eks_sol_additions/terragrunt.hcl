# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(prod-afms): update platform/cet_eks_sol_additions to

terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-component-eks/tree/cet-eks-sol-additions/v1.0.2/modules/cet-eks-sol-additions
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-component-eks.git//modules/cet-eks-sol-additions?ref=cet-eks-sol-additions/v1.0.2"
}

prevent_destroy = true

include {
  path = find_in_parent_folders()
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}

dependency "vpc" {
  config_path = "../../network/vpc"
}

dependency "dns" {
  config_path = "../../network/dns"
}

dependency "cet_eks" {
  config_path = "../cet_eks"
}

inputs = {
  tags = dependency.account_config.outputs.mandatory_tags

  cluster_name = dependency.cet_eks.outputs.cluster_name
  kms_key_arn  = dependency.cet_eks.outputs.kms_key_arn

  vpc_id              = dependency.vpc.outputs.vpc_id
  subnetgroup_private = dependency.vpc.outputs.subnetgroup_private

  public_hosted_zone_id  = dependency.dns.outputs.internal_public.zone_id
  private_hosted_zone_id = dependency.dns.outputs.internal_private.zone_id
  domain_name            = dependency.dns.outputs.internal_public.domain_name

  datadog_kms_key_arn = ""

  datadog = {
    enabled = false
  }

  prometheus = {
    enabled = false
  }
  fluentbit = {
    enabled = false
  }
  interruption_handling = {
    enabled = false
  }
  istio = {
    enabled = false
  }
}
