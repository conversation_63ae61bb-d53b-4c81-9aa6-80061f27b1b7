terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-ISS/cet-eks-o11y/tree/v0.3.0/terraform
  source = "git::https://github.vodafone.com/VFDE-ISS/cet-eks-o11y.git//terraform?ref=v0.3.0"
}

prevent_destroy = true

include {
  path = find_in_parent_folders()
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}

dependency "cet_eks" {
  config_path = "../cet_eks"
}

inputs = {
  tags         = dependency.account_config.outputs.mandatory_tags
  cluster_name = dependency.cet_eks.outputs.cluster_name
}
