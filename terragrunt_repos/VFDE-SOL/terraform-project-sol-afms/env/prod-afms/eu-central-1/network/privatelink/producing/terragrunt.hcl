terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-component-privatelink/tree/v0.5.0/modules/endpoint/producing
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-component-privatelink.git//modules/endpoint/producing?ref=v0.5.0&depth=1"
}

dependency "tfremotestate" {
  config_path  = "../../../foundation/tfremotestate"
  skip_outputs = true
}

dependency "account_config" {
  config_path = "../../../foundation/account_config"
}

dependency "notification" {
  config_path = "../../../foundation/notification"
}

dependency "account_index_mgmt" {
  config_path = "../../../foundation/account_index_mgmt"
}

dependency "account_index_asgw" {
  config_path = "../../../foundation/account_index_asgw"
}

dependency "nlb" {
  config_path = "../../private_svc_nlb"
}

dependency "dns" {
  config_path = "../../dns"
}

dependency "vpc" {
  config_path = "../../vpc"
}

prevent_destroy = false

include {
  path = find_in_parent_folders()
}

inputs = {
  allowed_account_ids = {
    # legacy landing zone
    connectivity_mgmt = ************

    # VFCF/CET Accounts
    mgmt      = dependency.account_index_mgmt.outputs.index.by_short_alias.mgmt.account_id
    prod_asgw = dependency.account_index_asgw.outputs.index.by_short_alias.prod-asgw.account_id

  }

  params_map = {
    "eks_svc_nlb_arn" = dependency.nlb.outputs.arn
  }

  meta_hosted_zone_id    = dependency.dns.outputs.meta.zone_id
  public_hosted_zone_id  = dependency.dns.outputs.internal_public.zone_id
  private_hosted_zone_id = dependency.dns.outputs.internal_private.zone_id

  vpc_id          = dependency.vpc.outputs.vpc_id
  vpc_cidr        = dependency.vpc.outputs.vpc_cidr
  subnet_ids      = dependency.vpc.outputs.subnetgroup_private.subnets
  route_table_ids = concat(dependency.vpc.outputs.subnetgroup_private.route_tables, dependency.vpc.outputs.subnetgroup_eks.route_tables, dependency.vpc.outputs.subnetgroup_public.route_tables)

  account_short_alias = dependency.account_config.outputs.account.short_alias
  tags                = dependency.account_config.outputs.mandatory_tags
}
