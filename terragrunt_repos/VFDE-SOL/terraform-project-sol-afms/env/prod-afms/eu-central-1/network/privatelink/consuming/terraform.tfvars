# Deploymemnt guide with configuration examples: https://github.vodafone.com/pages/VFDE-SOL/docs-sol-cet/docs/developer_guide/technical_solutions/privatelink_guide.html#consuming-endpoints

connected_account_domain_names = [
  "internal.vodafone.com",
  "fra.tssa.vodafone.com",
  "mgmt.sol-vf.de",
  "aws.solstice.vodafone.com",
  "asgw.sol-vf.de"
]
vodafone_endpoints = {
  vf_cribl_stream = { deploy = true },
  vf_deployvip    = { deploy = false },
  vf_trend_micro  = { deploy = false },
  vf_smtp_relay   = { deploy = false },
  eks_tools_mgmt  = { deploy = false }
}

consuming_endpoints = {
  cdh_cim_prod_kafka = {
    deploy       = true
    service_name = "com.amazonaws.vpce.eu-central-1.vpce-svc-07f4a990afe320f36"
    security_groups = { # allow kafka
      ingress = [{
        from_port   = 9000
        to_port     = 9006
        protocol    = "tcp"
        cidr_blocks = "vpc_cidrs"
        },
        { # kafka schema registry
          from_port   = 8081
          to_port     = 8082
          protocol    = "tcp"
          cidr_blocks = "vpc_cidrs"
        },
        { # kafka brokers
          from_port   = 9443
          to_port     = 9443
          protocol    = "tcp"
          cidr_blocks = "vpc_cidrs"
        },
        {
          from_port   = 9444
          to_port     = 9444
          protocol    = "tcp"
          cidr_blocks = "vpc_cidrs"
      }],
      # egress = []
    }

    dns = [
      {
        id        = "main"
        zone_name = "aws.solstice.vodafone.com"
        name      = "kafka-lb.prod.prod04"
      },
    ]
  }
  tool_prod_asgw = {
    deploy       = true
    service_name = "com.amazonaws.vpce.eu-central-1.vpce-svc-006b6d56da7d152d0"
    subnet_ids   = "endpoint_subnets"
    security_groups = {
      ingress = [{ # allow TLS
        from_port   = 443
        to_port     = 443
        protocol    = "tcp"
        cidr_blocks = "vpc_cidrs"
      }],
      # egress = []
    }
    dns = [
      {
        id        = "main"
        zone_name = "asgw.sol-vf.de"
        name      = "api.prod"
      },
    ]
  }
}
