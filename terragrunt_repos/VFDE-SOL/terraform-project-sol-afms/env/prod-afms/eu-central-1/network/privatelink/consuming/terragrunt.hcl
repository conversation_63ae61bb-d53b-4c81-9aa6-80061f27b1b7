# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(dev-afms): update network/privatelink/consuming

terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-component-privatelink/tree/v0.6.0/modules/endpoint/consuming
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-component-privatelink.git//modules/endpoint/consuming?ref=v0.6.0&depth=1"
}

dependency "tfremotestate" {
  config_path  = "../../../foundation/tfremotestate"
  skip_outputs = true
}

dependency "account_config" {
  config_path = "../../../foundation/account_config"
}

dependency "notification" {
  config_path = "../../../foundation/notification"
}

dependency "vpc" {
  config_path = "../../vpc"
}

prevent_destroy = false

include {
  path = find_in_parent_folders()
}

inputs = {
  params_map = { # map here string values from .tfvars to real variables or values
    "vpc_cidrs" = dependency.vpc.outputs.vpc_cidr
  }

  vpc_id     = dependency.vpc.outputs.vpc_id
  vpc_cidr   = dependency.vpc.outputs.vpc_cidr
  subnet_ids = dependency.vpc.outputs.subnetgroup_private.subnets

  tags = dependency.account_config.outputs.mandatory_tags
}
