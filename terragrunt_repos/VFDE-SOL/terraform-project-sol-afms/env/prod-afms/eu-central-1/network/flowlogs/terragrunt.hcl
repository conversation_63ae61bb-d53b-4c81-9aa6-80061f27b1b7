# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(pref-afms): update network/flowlogs to

terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol/tree/network/flowlogs/v0.2.4/modules/network/flowlogs
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol.git//modules/network/flowlogs?ref=network/flowlogs/v0.2.4"
}

prevent_destroy = true

include {
  path = find_in_parent_folders()
}

dependency "vpc" {
  config_path = "../vpc"
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}

dependency "tfremotestate" {
  config_path  = "../../foundation/tfremotestate"
  skip_outputs = true
}

inputs = {
  tags   = dependency.account_config.outputs.mandatory_tags
  vpc_id = dependency.vpc.outputs.vpc_id
}
