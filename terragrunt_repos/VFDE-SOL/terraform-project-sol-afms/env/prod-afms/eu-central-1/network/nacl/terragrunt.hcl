# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(<REPLACE_WITH_ENV_NAME>): update network/nacl to

terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol/tree/network/nacl/v0.2.1/modules/network/nacl
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol.git//modules/network/nacl?ref=network/nacl/v0.2.1"
}

prevent_destroy = true

include {
  path = find_in_parent_folders()
}

dependency "vpc" {
  config_path = "../vpc"
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}

dependency "tfremotestate" {
  config_path  = "../../foundation/tfremotestate"
  skip_outputs = true
}

inputs = {
  vpc_id                 = dependency.vpc.outputs.vpc_id
  subnetgroup_private    = dependency.vpc.outputs.subnetgroup_private
  subnetgroup_public     = dependency.vpc.outputs.subnetgroup_public
  subnetgroup_eks        = dependency.vpc.outputs.subnetgroup_eks
  subnetgroup_eks_master = dependency.vpc.outputs.subnetgroup_eks_master
  subnetgroup_eks_node   = dependency.vpc.outputs.subnetgroup_eks_node
  tags                   = dependency.account_config.outputs.mandatory_tags
}
