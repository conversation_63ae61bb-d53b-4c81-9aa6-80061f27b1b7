# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(<REPLACE_WITH_ENV_NAME>): update network/dns to

terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol/tree/network/dns/v0.3.0/modules/network/dns
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol.git//modules/network/dns?ref=network/dns/v0.3.0"
}

prevent_destroy = true

dependency "tfremotestate" {
  config_path  = "../../foundation/tfremotestate"
  skip_outputs = true
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}

dependency "vpc" {
  config_path = "../vpc"
}

include {
  path = find_in_parent_folders()
}

inputs = {
  vpc_id              = dependency.vpc.outputs.vpc_id
  account_short_alias = dependency.account_config.outputs.account.short_alias
  enabled_pub_domain  = true
  tags                = dependency.account_config.outputs.mandatory_tags
}
