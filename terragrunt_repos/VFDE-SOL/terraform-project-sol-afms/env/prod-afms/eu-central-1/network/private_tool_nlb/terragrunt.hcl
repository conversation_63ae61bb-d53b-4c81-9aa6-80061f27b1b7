terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol/tree/network/private_tool_nlb/v0.1.1/modules/network/private_tool_nlb
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol//modules/network/private_tool_nlb?ref=network/private_tool_nlb/v0.1.1"
}

prevent_destroy = true

include {
  path = find_in_parent_folders()
}


dependency "account_config" {
  config_path = "../../foundation/account_config"
}

dependency "tfremotestate" {
  config_path  = "../../foundation/tfremotestate"
  skip_outputs = true
}

dependency "vpc" {
  config_path = "../vpc"
}

dependency "dns" {
  config_path = "../dns"
}


inputs = {

  vpc_id             = dependency.vpc.outputs.vpc_id
  private_subnet_ids = dependency.vpc.outputs.subnetgroup_private.subnets

  internal_public_hosted_zone_id  = dependency.dns.outputs.internal_public.zone_id
  internal_private_hosted_zone_id = dependency.dns.outputs.internal_private.zone_id
  internal_private_domain_name    = dependency.dns.outputs.internal_private.domain_name

  tags = dependency.account_config.outputs.mandatory_tags

}
