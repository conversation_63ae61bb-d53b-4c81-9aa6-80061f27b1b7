terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol/tree/network/public_gh_auth_alb/v2.18.0/modules/network/public_gh_auth_alb
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol//modules/network/public_gh_auth_alb?ref=network/public_gh_auth_alb/v2.18.0"
}

prevent_destroy = false

include {
  path = find_in_parent_folders()
}

dependency "public_gh_auth_cert" {
  config_path = "../../../us-east-1/network/public_gh_auth_cert"
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}

dependency "vpc" {
  config_path = "../vpc"
}

dependency "dns" {
  config_path = "../dns"
}

locals {
  list = split("/", path_relative_to_include())

  name = replace(element(local.list, length(local.list) - 1), "_", "-")
}

inputs = {
  tags = dependency.account_config.outputs.mandatory_tags

  name = local.name

  github_oauth_app_id = "6f16d8e5dc85511644e7"

  albs = {
    (local.name) : {
      vpc_id         = dependency.vpc.outputs.vpc_id
      vpc_cidr       = dependency.vpc.outputs.vpc_cidr
      subnet_ids     = dependency.vpc.outputs.subnetgroup_public.subnets
      domain_name    = dependency.dns.outputs.pub.domain_name
      hosted_zone_id = dependency.dns.outputs.pub.zone_id
      waf_rule_exceptions = [
        {
          name = "AWSManagedRulesCommonRuleSet"
          managed_rule_group_statement = {
            rule_action_overrides = [
              {
                name = "SizeRestrictions_BODY"
                action_to_use = {
                  count = {}
                }
              }
            ]
          }
        }
      ],
      application_subdomains = [
        "alertmanager",
        "thanos-querier",
        "grafana",
        "hubble",
        "portal"
      ]
    },
    "${local.name}-websrv" : {
      vpc_id            = dependency.vpc.outputs.vpc_id
      vpc_cidr          = dependency.vpc.outputs.vpc_cidr
      subnet_ids        = dependency.vpc.outputs.subnetgroup_public.subnets
      domain_name       = dependency.dns.outputs.pub.domain_name
      hosted_zone_id    = dependency.dns.outputs.pub.zone_id
      target_port       = 80
      health_check_port = 9091
      waf_rule_exceptions = [
        {
          name = "AWSManagedRulesCommonRuleSet"
          managed_rule_group_statement = {
            rule_action_overrides = [
              {
                name = "SizeRestrictions_BODY"
                action_to_use = {
                  count = {}
                }
              }
            ]
          }
        }
      ],
      application_subdomains = ["fraud-view"]
      target_groups = {
        ip = {
          name_prefix                   = "websrv"
          port                          = 80
          protocol                      = "HTTP"
          protocol_version              = "HTTP1"
          vpc_id                        = dependency.vpc.outputs.vpc_id
          load_balancing_algorithm_type = "least_outstanding_requests"
          target_type                   = "instance"
          create_attachment             = false

          health_check = {
            enabled             = true
            port                = 80
            protocol            = "HTTP"
            healthy_threshold   = 5
            unhealthy_threshold = 5
            interval            = 30
            path                = "/"
          }

          # Stickiness configuration
          stickiness = {
            enabled         = true
            type            = "lb_cookie"
            cookie_duration = 86400 # Timeout of 1 day in seconds
          }
          deregistration_delay = 0

        }
      }
    }
  }

  auth_domain_name    = dependency.dns.outputs.pub.domain_name
  auth_hosted_zone_id = dependency.dns.outputs.pub.zone_id

  auth_domain_certificate_arn = dependency.public_gh_auth_cert.outputs.arn
}
