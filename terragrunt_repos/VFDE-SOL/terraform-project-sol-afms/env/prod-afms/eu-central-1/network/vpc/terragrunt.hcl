# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(<REPLACE_WITH_ENV_NAME>): update network/vpc to

terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol/tree/network/vpc/v1.0.1/modules/network/vpc
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol.git//modules/network/vpc?ref=network/vpc/v1.0.1"
}

prevent_destroy = true

include {
  path = find_in_parent_folders()
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}

dependency "tfremotestate" {
  config_path  = "../../foundation/tfremotestate"
  skip_outputs = true
}

inputs = {
  cidr_segment         = 80
  enable_cet_eks       = true
  cet_eks_cluster_name = "eks"
  tags                 = dependency.account_config.outputs.mandatory_tags
}
