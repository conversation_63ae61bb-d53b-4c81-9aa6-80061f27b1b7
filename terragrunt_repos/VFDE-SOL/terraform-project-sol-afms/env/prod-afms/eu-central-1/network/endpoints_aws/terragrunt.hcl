# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(dev-afms): update network/endpoints_aws to

terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol/tree/network/privatelink/endpoints_aws/v0.4.0/modules/network/privatelink/endpoints_aws
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol.git//modules/network/privatelink/endpoints_aws?ref=network/privatelink/endpoints_aws/v0.4.0"
}

prevent_destroy = false

include {
  path = find_in_parent_folders()
}

dependency "vpc" {
  config_path = "../vpc"
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}

dependency "tfremotestate" {
  config_path  = "../../foundation/tfremotestate"
  skip_outputs = true
}

inputs = {
  endpoints = {
    cloudtrail = {
      service = "cloudtrail"
    },
    config = {
      service = "config"
    },
    s3 = {
      service      = "s3"
      service_type = "Gateway"
    },
    dynamodb = {
      service      = "dynamodb"
      service_type = "Gateway"
    },
    ebs = {
      service = "ebs"
    },
    ec2 = {
      service = "ec2"
    },
    ec2messages = {
      service = "ec2messages"
    },
    kms = {
      service = "kms"
    },
    logs = {
      service = "logs"
    },
    monitoring = {
      service = "monitoring"
    },
    ssm = {
      service = "ssm"
    },
    ssmmessages = {
      service = "ssmmessages"
    },
    sts = {
      service = "sts"
    },
    ecs = {
      service = "ecs"
    },
    ecr-dkr = {
      service = "ecr.dkr"
    },
    ecr-api = {
      service = "ecr.api"
    }
  }

  vpc_id          = dependency.vpc.outputs.vpc_id
  vpc_cidr        = dependency.vpc.outputs.vpc_cidr
  route_table_ids = concat(dependency.vpc.outputs.subnetgroup_private.route_tables, dependency.vpc.outputs.subnetgroup_eks.route_tables, dependency.vpc.outputs.subnetgroup_public.route_tables)
  subnet_ids      = dependency.vpc.outputs.subnetgroup_private.subnets
  tags            = dependency.account_config.outputs.mandatory_tags
}
