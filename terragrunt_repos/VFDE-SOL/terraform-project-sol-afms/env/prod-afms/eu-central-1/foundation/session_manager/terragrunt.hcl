# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(dev-bmt): update foundation/session_manager to

terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol/tree/foundation/session_manager/v0.2.2/modules/foundation/session_manager
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol.git//modules/foundation/session_manager?ref=foundation/session_manager/v0.2.2"
}

prevent_destroy = false

dependency "tfremotestate" {
  config_path  = "../tfremotestate"
  skip_outputs = true
}

dependency "account_config" {
  config_path = "../account_config"
}

include {
  path = find_in_parent_folders()
}

inputs = {
  tags                  = dependency.account_config.outputs.mandatory_tags
  s3_bucket_key_enabled = true
}
