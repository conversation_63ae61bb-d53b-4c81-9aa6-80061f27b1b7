# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(test-afms): update foundation/security_hub_standard_aws_foundational to

terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol/tree/foundation/security_hub_standard_aws_foundational/v0.3.1/modules/foundation/security_hub_standard_aws_foundational
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol.git//modules/foundation/security_hub_standard_aws_foundational?ref=foundation/security_hub_standard_aws_foundational/v0.3.1"
}

prevent_destroy = true

include {
  path = find_in_parent_folders()
}

inputs = {
  disabled_controls = {
    #Common
    hardware_mfa_for_root_account = {
      id              = "IAM.6"
      disabled_reason = "Account Owner Responsibility"
    }
    aws_config_enabled = {
      id              = "Config.1"
      disabled_reason = "False positive. AWS Config is enabled"
    }
    vpc_network_acl_unused_check = {
      id              = "EC2.16"
      disabled_reason = "Vodafone Compliance enforces empty default nacls"
    }
    nacl_no_unrestricted_ssh_rdp = {
      id              = "EC2.21"
      disabled_reason = "Hight port is also used by other services"
    }
    ec2_transit_gateway_auto_vpc_attach_disabled = {
      id              = "EC2.23"
      disabled_reason = "Not in our control"
    }
    s3_bucket_logging_enabled = {
      id              = "S3.9"
      disabled_reason = "Taken care by pcs cloudtrail"
    }
    cloud_trail_cloud_watch_logs_enabled = {
      id              = "CloudTrail.5"
      disabled_reason = "This complains on org trail pcs"
    }
    iam_authentication_enabled = {
      id              = "RDS.10"
      disabled_reason = "IAM Authentication is currently not supported by applications"
    }
    cluster_iam_authentication_enabled = {
      id              = "RDS.12"
      disabled_reason = "IAM Authentication is currently not supported by applications"
    }
    automatic_minor_version_upgrades = {
      id              = "RDS.13"
      disabled_reason = "Versions are specified by the vendor"
    }
    rds_no_default_ports = {
      id              = "RDS.23"
      disabled_reason = "Don't believe in security by obfuscation"
    }
    dynamodb_autoscaling_enabled = {
      id              = "DynamoDB.1"
      disabled_reason = "autoscaling is overkill for tf state locking"
    }
    dynamodb_pitr_enabled = {
      id              = "DynamoDB.2"
      disabled_reason = "pitr is overkill for tf state locking"
    }
    elasticsearch_audit_logging_enabled = {
      id              = "ES.5"
      disabled_reason = "Not needed at the moment"
    }
    elasticsearch_primary_node_fault_tolerance = {
      id              = "ES.7"
      disabled_reason = "No need fo dedicated master nodes at the moment"
    }
    opensearch_audit_logging_enabled = {
      id              = "Opensearch.5"
      disabled_reason = "Not needed at the moment"
    }
    secretsmanager_rotation_enabled_check = {
      id              = "SecretsManager.1"
      disabled_reason = "SM Only used by Datadog Forwarder which does not support rotation"
    }
  }
}
