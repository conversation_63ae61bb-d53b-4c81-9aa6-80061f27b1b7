# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(dev-afms): update foundation/security_hub_notification

terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol/tree/foundation/security_hub_notification/v0.2.0/modules/foundation/security_hub_notification
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol.git//modules/foundation/security_hub_notification?ref=foundation/security_hub_notification/v0.2.0"
}

dependency "account_config" {
  config_path = "../../foundation/account_config"
}

dependency "notification" {
  config_path = "../notification"
}


prevent_destroy = false

include {
  path = find_in_parent_folders()
}

inputs = {
  tags                           = dependency.account_config.outputs.mandatory_tags
  sns_topi_arn                   = dependency.notification.outputs.topic_map["alarm-topic-arn"]
  additional_iam_policies        = [dependency.notification.outputs.iam_access_policy_arn]
  filter_finding_product         = ["securityhub"]
  filter_finding_severity        = ["CRITICAL", "HIGH"]
  filter_finding_workflow_status = ["NEW"]
}
