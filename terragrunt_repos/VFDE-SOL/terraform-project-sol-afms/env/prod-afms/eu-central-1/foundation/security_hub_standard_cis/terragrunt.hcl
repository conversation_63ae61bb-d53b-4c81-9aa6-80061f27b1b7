# !!!Dont delete the comment line below, otherwise you disable the centralized version sync for this component!!!
# renovate: commitPrefix = feat(test-afms): update foundation/security_hub_standard_cis to

locals {
  additional_metric_filter_controls = {
    for id in toset(formatlist("3.%s", range(1, 10))) :
    "ensure_metric_filter_alarms_cloudtrail_${id}" => {
      id              = id
      disabled_reason = "Organisational Trail in place with metric filter alarms"
    }
  }
}

terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-modules-sol/tree/foundation/security_hub_standard_cis/v0.4.0/modules/foundation/security_hub_standard_cis
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-modules-sol.git//modules/foundation/security_hub_standard_cis?ref=foundation/security_hub_standard_cis/v0.4.0"
}

prevent_destroy = true

include {
  path = find_in_parent_folders()
}

inputs = {
  disabled_controls = merge(local.additional_metric_filter_controls, {
    ensure_mfa_is_enabled_for_root_account = {
      id              = "1.14"
      disabled_reason = "Account Owner Responsibility"
    }
  })
}
