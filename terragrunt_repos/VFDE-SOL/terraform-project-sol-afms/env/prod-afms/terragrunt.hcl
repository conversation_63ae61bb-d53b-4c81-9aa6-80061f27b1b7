#terragrunt_version_constraint = ">= 0.31.3"
#terraform_version_constraint = ">= 1.0.0"

locals {
  component_path     = path_relative_to_include()
  region_vars        = read_terragrunt_config(find_in_parent_folders("region.hcl"))
  aws_region         = local.region_vars.locals.aws_region
  aws_backend_region = local.region_vars.locals.aws_backend_region
  account_id         = "************"
}

generate "provider" {
  path      = "provider.tf"
  if_exists = "overwrite_terragrunt"
  contents  = <<EOF
provider "aws" {
  region = "${local.aws_region}"
  # Only these AWS Account IDs may be operated on by this template
  allowed_account_ids = ["${local.account_id}"]
}
EOF
}

remote_state {
  backend = "s3"
  config = {
    # bucket update is disabled, otherwise tg >= v0.37.0 trys to modify bucket policies
    disable_bucket_update = true
    encrypt               = true
    bucket                = "${local.account_id}-terraform-state"
    key                   = "${local.component_path}/terraform.tfstate"
    kms_key_id            = "arn:aws:kms:${local.aws_backend_region}:${local.account_id}:alias/comp-tfremotestate"
    region                = local.aws_backend_region
    dynamodb_table        = "${local.account_id}-terraform-lock"
  }
  generate = {
    path      = "backend.tf"
    if_exists = "overwrite_terragrunt"
  }
}
