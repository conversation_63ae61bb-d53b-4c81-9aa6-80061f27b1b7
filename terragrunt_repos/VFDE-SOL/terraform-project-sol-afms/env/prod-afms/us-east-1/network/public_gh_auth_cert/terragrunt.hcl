terraform {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-SOL/terraform-component-cognito-github-openid-wrapper/tree/v0.1.1/extras/auth_domain_certificate
  source = "git::https://github.vodafone.com/VFDE-SOL/terraform-component-cognito-github-openid-wrapper//extras/auth_domain_certificate?ref=v0.1.1"

}

prevent_destroy = false

include {
  path = find_in_parent_folders()
}

dependency "dns" {
  config_path = "../../../eu-central-1/network/dns"
}

dependency "tfremotestate" {
  config_path  = "../../../eu-central-1/foundation/tfremotestate"
  skip_outputs = true
}

dependency "account_config" {
  config_path = "../../../eu-central-1/foundation/account_config"
}



inputs = {
  tags = merge(
    {
      "Terraform_Repository" = "https://github.vodafone.com/VFDE-SOL/terraform-component-alb-gh-openid"
      "Terraform_Module"     = "auth_domain_certificate"
    }, dependency.account_config.outputs.mandatory_tags
  )

  public_hosted_zone_name = dependency.dns.outputs.pub.domain_name

}
