variable "cluster_name" {
  description = "cluster name"
  type        = string
  default     = "eks"
}

variable "tags" {
  description = "tags"
  type        = map(any)
}

variable "cloudwatch_prometheus_exporter_namespace" {
  description = "Namespace of cloudwatch prometheus exporter"
  type        = string
}

variable "cloudwatch_prometheus_exporter_serviceAccountName" {
  description = "SA for cloudwatch prometheus exporter"
  type        = string
}
