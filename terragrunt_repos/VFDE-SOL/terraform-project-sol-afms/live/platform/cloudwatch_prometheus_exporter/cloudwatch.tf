###################### METRICS READ ACCESS ######################
data "aws_iam_policy_document" "cloudwatch_metrics_read_access_irsa" {
  statement {
    sid = "AllowReadingMetricsFromCloudWatch"
    actions = [
      "tag:GetResources",
      "cloudwatch:GetMetricData",
      "cloudwatch:GetMetricStatistics",
      "cloudwatch:ListMetrics",
      "aps:ListWorkspaces",
      "autoscaling:DescribeAutoScalingGroups",
      "dms:DescribeReplicationInstances",
      "dms:DescribeReplicationTasks",
      "ec2:DescribeTransitGatewayAttachments",
      "ec2:DescribeSpotFleetRequests",
      "shield:ListProtections",
      "storagegateway:ListGateways",
      "storagegateway:ListTagsForResource"
    ]
    resources = ["*"]
  }
}

module "cloudwatch_metrics_read_access_irsa" {
  # Git_Auto_Ref: https://github.vodafone.com/VFDE-ISS/cet-eks/tree/v0.2.0/terraform/modules/irsa
  source = "git::https://github.vodafone.com/VFDE-ISS/cet-eks//terraform/modules/irsa?ref=v0.2.0"

  cluster_name              = var.cluster_name
  service_account_namespace = var.cloudwatch_prometheus_exporter_namespace
  service_account_name      = var.cloudwatch_prometheus_exporter_serviceAccountName

  policy_json = data.aws_iam_policy_document.cloudwatch_metrics_read_access_irsa.json

  tags = local.tags
}
