output "account_index" {
  value       = module.accountindex.index
  description = "Account index of this repo. Holds account info of all accounts that belong to this repo"
}

output "project" {
  value       = module.accountindex.project
  description = "Name of the project to which the accounts of this index belong"
}

output "account" {
  value       = local.account
  description = "Returns an account object of the account where this code gets applied to"
}

output "mandatory_tags" {
  value       = local.combined_tags
  description = "A map containing mandatory tags"
}
