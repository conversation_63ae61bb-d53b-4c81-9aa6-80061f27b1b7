package ghtags

import (
	"context"
	"fmt"
	"sync"
	"time"

	"log/slog"

	"github.com/google/go-github/github"
	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/core"
	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/helpers/clients/ghes"
)

type service struct {
	logger         *slog.Logger
	coreFuncs      core.DataServiceInjector
	gh             ghes.Client
	lastUpdate     sync.Map // values of type time.Time
	updateCooldown time.Duration
	host           string
	maxConcurrency int
	maxPages       int
	repoQueue      chan string // Repository URLs to process
	wg             sync.WaitGroup
	processedRepos sync.Map // Track repositories being processed to avoid duplicates
}

type ServiceArguments struct {
	GithubClient   ghes.Client
	UpdateCooldown time.Duration
	MaxConcurrency int // Maximum number of concurrent GitHub API calls
	MaxPages       int // Maximum number of tag pages to fetch per repository
}

func NewService(args *ServiceArguments) (core.Service, error) {
	s := service{
		gh: args.GithubClient,
	}

	if args.UpdateCooldown.Abs() > 0 {
		s.updateCooldown = args.UpdateCooldown
	} else {
		s.updateCooldown = 15 * time.Minute
	}

	// Set concurrency defaults
	if args.MaxConcurrency > 0 {
		s.maxConcurrency = args.MaxConcurrency
	} else {
		s.maxConcurrency = 5 // Default to 5 concurrent requests
	}

	if args.MaxPages > 0 {
		s.maxPages = args.MaxPages
	} else {
		s.maxPages = 3 // Default to first 3 pages (300 tags max per repo)
	}

	s.host = s.gh.Client().BaseURL.Host
	s.repoQueue = make(chan string, 100) // Buffer for repository URLs to process

	return &s, nil
}

func (s *service) Inject(injector core.DataServiceInjector) {
	s.coreFuncs = injector
}

func (s *service) InjectLogger(logger *slog.Logger) {
	s.logger = logger
}

func (s *service) Start() error {
	s.logger.Debug("github tags update configured", "cooldownMinutes", s.updateCooldown.Minutes(), "host", s.host, "maxConcurrency", s.maxConcurrency, "maxPages", s.maxPages)

	// Start worker goroutines
	for i := 0; i < s.maxConcurrency; i++ {
		s.wg.Add(1)
		go s.worker()
	}

	return nil
}

func (s *service) Stop() error {
	close(s.repoQueue)
	s.wg.Wait()
	return nil
}

func (s *service) InstanceHandlers() []core.InstanceHandler {
	return []core.InstanceHandler{
		s.HandleInstance,
	}
}

func (s *service) ID() string {
	return "ghtags"
}

func (s *service) HandleInstance(instance core.Instance) error {
	if instance.Version.Repository.Host != s.host {
		s.logger.Debug("skipping instance from different host", "instanceHost", instance.Version.Repository.Host, "serviceHost", s.host)
		return nil
	}

	repoURL := instance.Version.Repository.URL
	s.logger.Debug("processing instance for repository", "repo", repoURL)

	// Check cooldown
	last, found := s.lastUpdate.Load(repoURL)
	if found {
		lastTime := last.(time.Time)
		if time.Since(lastTime) < s.updateCooldown {
			s.logger.Debug("refresh tags is still on cooldown", "repo", instance.Version.Repository.SlogAttributes(), "lastUpdate", lastTime, "cooldown", s.updateCooldown)
			return nil
		}
	}

	// Check if this repository is already being processed or queued
	if _, alreadyProcessing := s.processedRepos.LoadOrStore(repoURL, true); alreadyProcessing {
		s.logger.Debug("repository already queued for processing", "repo", repoURL)
		return nil
	}

	// Queue the repository for processing by workers
	select {
	case s.repoQueue <- repoURL:
		// Successfully queued
		s.logger.Debug("repository queued for processing", "repo", repoURL)
	default:
		// Queue is full, remove from processed map and process synchronously as fallback
		s.processedRepos.Delete(repoURL)
		s.logger.Warn("worker queue full, processing synchronously", "repo", repoURL)
		return s.processRepositoryByURL(repoURL)
	}

	return nil
}

func (s *service) worker() {
	defer s.wg.Done()
	s.logger.Debug("GitHub tags worker started")

	for repoURL := range s.repoQueue {
		s.logger.Debug("worker processing repository", "repo", repoURL)

		if err := s.processRepositoryByURL(repoURL); err != nil {
			s.logger.Error("failed to process repository", "error", err, "repo", repoURL)
			// Don't return on error, continue processing other repositories
		}

		// Remove from processed map when done (success or failure)
		s.processedRepos.Delete(repoURL)
		s.logger.Debug("worker completed processing repository", "repo", repoURL)
	}

	s.logger.Debug("GitHub tags worker stopped")
}

func (s *service) processRepositoryByURL(repoURL string) error {
	// Parse repository from URL
	repo := core.RepositoryFromURL(repoURL)
	s.logger.Info("processing repository for tags", "repo", repo.SlogAttributes())

	var tags []*github.RepositoryTag
	var result []string
	page := 0

	// Limit the number of pages we fetch
	for page < s.maxPages {
		s.logger.Debug("fetching tags page", "repo", repoURL, "page", page)

		tagsPage, res, err := s.gh.Client().Repositories.ListTags(
			context.Background(),
			repo.Owner,
			repo.Name,
			&github.ListOptions{
				PerPage: 100,
				Page:    page,
			})
		if err != nil {
			s.logger.Error("failed to list tags", "repo", repoURL, "page", page, "error", err)
			return fmt.Errorf("failed to list tags for %s at page %d: %w", repoURL, page, err)
		}

		s.logger.Info("ghtags list tags", "page", page, "status", res.Status, "repo", repoURL, "tagsCount", len(tagsPage))
		if len(tagsPage) == 0 {
			s.logger.Debug("no more tags found, stopping pagination", "repo", repoURL, "page", page)
			break
		}
		tags = append(tags, tagsPage...)
		page++
	}

	for _, tag := range tags {
		result = append(result, *tag.Name)
	}

	s.logger.Info("finished fetching tags from GitHub", "repo", repo.SlogAttributes(), "totalTags", len(tags), "pages", page)

	versions := tagsToLatestVersions(repo, result)
	s.logger.Debug("processed tags to versions", "repo", repoURL, "versionsFound", len(versions))

	for _, v := range versions {
		if err := s.coreFuncs.StoreVersion(v); err != nil {
			s.logger.Error("failed to store version", "repo", repoURL, "version", v.FullVersion, "error", err)
		} else {
			s.logger.Debug("stored version", "repo", repoURL, "version", v.FullVersion)
		}
	}

	s.lastUpdate.Store(repoURL, time.Now())
	s.logger.Info("completed processing repository", "repo", repoURL, "versionsStored", len(versions))
	return nil
}

func tagsToLatestVersions(repo core.Repository, tags []string) []core.Version {
	highest := make(map[string]core.Version)
	skippedTags := 0
	processedTags := 0

	for _, tag := range tags {
		this := core.VersionFromString(repo, tag)
		thisSemver, thisIsSemver := this.Semver()
		if !thisIsSemver || this.PreRelease {
			skippedTags++
			continue // must not put non-semver or release candidates to map
		}

		processedTags++
		latest, exists := highest[this.Prefix]
		if !exists {
			highest[this.Prefix] = this
		} else {
			latestSemver, _ := latest.Semver() // cannot have error, filtered out while adding
			if thisSemver.GT(latestSemver) {
				highest[this.Prefix] = this
			}
		}
	}

	res := []core.Version{}
	for _, v := range highest {
		res = append(res, v)
	}

	// Log some statistics for debugging
	if len(tags) > 0 {
		slog.Debug("processed tags to versions",
			"repo", repo.URL,
			"totalTags", len(tags),
			"processedTags", processedTags,
			"skippedTags", skippedTags,
			"resultVersions", len(res))
	}

	return res
}
